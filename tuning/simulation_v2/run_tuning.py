"""
Main script to run hyperparameter tuning for Simulation_v2.

Usage:
1. Start MongoDB: mongod --dbpath /path/to/db --port 27017
2. Run main tuning: python run_tuning.py --tune --max_evals 1000
3. Run worker: python run_tuning.py --worker --max_evals 1000
4. Parse results: python run_tuning.py --parse

Author: Augment Agent
Date: 2025-01-05
"""

import os
import sys
import argparse
from datetime import datetime

# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/tuning/simulation_v2", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)

from tuning.simulation_v2.hyo_tuning_manager import SimulationV2Tuner, run_worker, parse_hyperopt_results


def main():
    parser = argparse.ArgumentParser(description='Hyperparameter tuning for Simulation_v2')
    parser.add_argument('--tune', action='store_true', help='Run main tuning process')
    parser.add_argument('--worker', action='store_true', help='Run as worker process')
    parser.add_argument('--parse', action='store_true', help='Parse and analyze results')
    parser.add_argument('--test', action='store_true', help='Test objective function')
    parser.add_argument('--max_evals', type=int, default=100, help='Maximum evaluations')
    parser.add_argument('--mongo_url', type=str, default=None, help='MongoDB URL')
    parser.add_argument('--data_path', type=str, default=None, help='Path to score data')
    parser.add_argument('--start_date', type=str, default='2022-06-01', help='Simulation start date')
    parser.add_argument('--end_date', type=str, default='2026-01-01', help='Simulation end date')
    parser.add_argument('--iterations', type=int, default=10, help='Number of simulation iterations')
    
    args = parser.parse_args()
    
    if args.tune:
        print("=== Starting Main Tuning Process ===")
        tuner = SimulationV2Tuner(
            data_path=args.data_path,
            start_date=args.start_date,
            end_date=args.end_date,
            num_iterations=args.iterations
        )
        
        results = tuner.tune_simulation(max_evals=args.max_evals, mongo_url=args.mongo_url)
        print(f"\nTuning completed. Results: {results}")
        
    elif args.worker:
        print("=== Starting Worker Process ===")
        run_worker(mongo_url=args.mongo_url, max_evals=args.max_evals)
        
    elif args.parse:
        print("=== Parsing Results ===")
        df = parse_hyperopt_results(mongo_url=args.mongo_url)
        if df is not None:
            print(f"Results parsed successfully. Shape: {df.shape}")
        
    elif args.test:
        print("=== Testing Objective Function ===")
        tuner = SimulationV2Tuner(
            data_path=args.data_path,
            start_date=args.start_date,
            end_date=args.end_date,
            num_iterations=args.iterations
        )
        
        result = tuner.test_objective()
        print(f"Test completed. Result: {result}")
        
    else:
        print("Please specify one of: --tune, --worker, --parse, or --test")
        parser.print_help()


if __name__ == "__main__":
    main()

"""
Hyperparameter Tuning Manager for Simulation_v2 using Hyperopt with MongoDB Backend

This module provides hyperparameter optimization for trading simulation using Simulation_v2 class.
Optimizes both simulation_config and score_config parameters to maximize CAGR and win_rate_deals.

Architecture based on tuning/sell_pattern/hyo_tuning_manager.py but adapted for Simulation_v2.

Author: Augment Agent
Date: 2025-01-05
"""

import os
import sys
import json
import pickle
import warnings
from datetime import datetime, timedelta
from functools import partial
from typing import Dict, Any, List, Tuple
import traceback

import numpy as np
import pandas as pd
from hyperopt import hp, fmin, tpe, STATUS_OK, STATUS_FAIL
from hyperopt.mongoexp import MongoTrials
from joblib import Memory
from pathos.multiprocessing import ProcessingPool as Pool
from pymongo import MongoClient

# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
current_dir = current_dir.replace("/tuning/simulation_v2", "")
os.chdir(current_dir)
sys.path.insert(0, current_dir)

from core_utils.constant import REDIS_HOST, MONGO_HOST, MONGO_PORT, JOBLIB_CACHE_DIR
from core_utils.redis_cache import EvalRedis
from webui.utils_v2 import Simulation_v2
from core_utils.warp_score import ScoreManager

warnings.simplefilter(action='ignore')

# Configuration
memory = Memory(location=f'{JOBLIB_CACHE_DIR}_simulation_tuning', verbose=0)
try:
    memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
except TypeError:
    # Fallback for older joblib versions - just skip cache reduction
    pass
redis_cache = EvalRedis(host=REDIS_HOST, db=2)  # Use different DB from sell_pattern

BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../'))
FPATH = os.path.join(BASE_DIR, 'ticker_v1a')
PATH = os.path.join(BASE_DIR, 'tuning/simulation_v2/hyperopt_results')
DATA_PATH = os.path.join(BASE_DIR, 'webui/score_v1.csv')
NUM_PROCS = 20

VERSION = 'v1'
TRACK_VERSION = 1

# MongoDB configuration
MONGO_DB = "hyperopt_simulation_v2_db"
EXPERIMENT_NAME = "simulation_v2_tuning"

# Ensure directories exist
os.makedirs(PATH, exist_ok=True)
os.makedirs(os.path.join(BASE_DIR, 'tuning/simulation_v2/logs'), exist_ok=True)


def safe_objective_wrapper(objective_func, params):
    """
    Safe wrapper for objective function to handle exceptions gracefully.
    
    Args:
        objective_func: The actual objective function
        params: Parameters from hyperopt
        
    Returns:
        Dict with loss and status
    """
    try:
        return objective_func(params)
    except Exception as e:
        print(f"Error in objective function: {e}")
        traceback.print_exc()
        return {
            'loss': np.inf,
            'status': STATUS_FAIL,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }


class SimulationV2Tuner:
    """
    Hyperparameter tuner for Simulation_v2 using hyperopt with MongoDB backend.
    
    Optimizes combination of CAGR and win_rate_deals from simulation results.
    """
    
    def __init__(self, data_path: str = None, start_date: str = '2022-06-01', 
                 end_date: str = '2026-01-01', num_iterations: int = 10):
        """
        Initialize tuner.
        
        Args:
            data_path: Path to score data CSV file
            start_date: Simulation start date
            end_date: Simulation end date  
            num_iterations: Number of simulation iterations for run_fast()
        """
        self.data_path = data_path or DATA_PATH
        self.start_date = start_date
        self.end_date = end_date
        self.num_iterations = num_iterations
        
        self.data = None
        self.search_space = self._define_search_space()
        self.default_params = self._get_default_params()
        
        # Load and prepare data
        self._load_data()
        
        # Setup logging
        self.log_file = os.path.join(BASE_DIR, 'tuning/simulation_v2/logs', 
                                   f'tuning_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
        
    def _load_data(self):
        """Load and prepare data for simulation."""
        try:
            print(f"Loading data from {self.data_path}")
            
            # Load data with required columns
            required_cols = ['time', 'ticker', 'close', 'price', 'score', 'volume', 'volume_1m_p50']
            self.data = pd.read_csv(self.data_path)
            
            # Check if required columns exist
            missing_cols = [col for col in required_cols if col not in self.data.columns]
            if missing_cols:
                print(f"Warning: Missing columns {missing_cols}, using available columns")
                available_cols = [col for col in required_cols if col in self.data.columns]
                self.data = self.data[available_cols]
            
            # Data preprocessing
            self.data['time'] = pd.to_datetime(self.data['time'])
            self.data = self.data.dropna()
            
            print(f"Data loaded successfully: {len(self.data)} rows, "
                  f"Date range: {self.data['time'].min()} to {self.data['time'].max()}")
            
        except Exception as e:
            print(f"Error loading data: {e}")
            raise
    
    def _define_search_space(self) -> Dict[str, Any]:
        """
        Define hyperopt search space for both simulation_config and score_config.
        
        Based on domain knowledge and avoiding overfitting with reasonable step sizes.
        
        Returns:
            Dictionary containing hyperopt search space definitions
        """
        return {
            # === Simulation Config Parameters ===
            'cutloss': hp.quniform('cutloss', 0.1, 0.3, 0.02),  # 10% - 30%, step 2%
            'cutloss_duration': hp.quniform('cutloss_duration', 5, 30, 5),  # 5-30 days, step 5
            'ratio_nav': hp.quniform('ratio_nav', 0.8, 1.0, 0.05),  # 80% - 100%, step 5%
            'ratio_deal': hp.quniform('ratio_deal', 0.05, 0.2, 0.01),  # 5% - 20%, step 1%
            'ratio_deal_volume': hp.quniform('ratio_deal_volume', 0.05, 0.2, 0.01),  # 5% - 20%, step 1%
            'review_frequency': hp.choice('review_frequency', ['daily', 'weekly', 'monthly']),
            'fee_buy_rate': hp.quniform('fee_buy_rate', 0.0005, 0.002, 0.0001),  # 0.05% - 0.2%
            'fee_sell_rate': hp.quniform('fee_sell_rate', 0.001, 0.003, 0.0001),  # 0.1% - 0.3%
            'score_sell': hp.quniform('score_sell', -0.5, 1.0, 0.1),  # -0.5 to 1.0, step 0.1
            'score_buy': hp.quniform('score_buy', 0.5, 2.0, 0.1),  # 0.5 to 2.0, step 0.1
            'gamma': hp.quniform('gamma', 0.5, 2.0, 0.1),  # 0.5 to 2.0, step 0.1
            'min_ratio_deal_nav': hp.quniform('min_ratio_deal_nav', 0.005, 0.02, 0.001),  # 0.5% - 2%
            
            # === Score Config Parameters ===
            'calibrate_kind': hp.choice('calibrate_kind', [None, 'auto', 'iso', 'platt']),
            'use_temperature': hp.choice('use_temperature', [True, False]),
            'temp_mode': hp.choice('temp_mode', ['brier', 'none']),
            'step_round': hp.quniform('step_round', 0.1, 0.5, 0.05),  # 0.1 to 0.5, step 0.05
            'clip_range_low': hp.quniform('clip_range_low', -2.0, 0.0, 0.2),  # -2.0 to 0.0
            'clip_range_high': hp.quniform('clip_range_high', 2.0, 4.0, 0.2),  # 2.0 to 4.0
        }
    
    def _get_default_params(self) -> Dict[str, Any]:
        """Get default parameter values for initial testing."""
        return {
            # Simulation defaults
            'cutloss': 0.2,
            'cutloss_duration': 15,
            'ratio_nav': 1.0,
            'ratio_deal': 0.1,
            'ratio_deal_volume': 0.1,
            'review_frequency': 'monthly',
            'fee_buy_rate': 0.001,
            'fee_sell_rate': 0.002,
            'score_sell': 0.0,
            'score_buy': 1.0,
            'gamma': 1.0,
            'min_ratio_deal_nav': 0.01,
            
            # Score defaults
            'calibrate_kind': None,
            'use_temperature': False,
            'temp_mode': 'brier',
            'step_round': 0.2,
            'clip_range_low': -1.0,
            'clip_range_high': 3.0,
        }

    def _params_to_configs(self, params: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Convert hyperopt params to simulation_config and score_config.

        Args:
            params: Parameters from hyperopt

        Returns:
            Tuple of (simulation_config, score_config)
        """
        # Build simulation config
        simulation_config = {
            'initial_amount': 1e9,  # Fixed
            'cutloss': float(params['cutloss']),
            'cutloss_duration': int(params['cutloss_duration']),
            'ratio_nav': float(params['ratio_nav']),
            'ratio_deal': float(params['ratio_deal']),
            'ratio_deal_volume': float(params['ratio_deal_volume']),
            'review_frequency': params['review_frequency'],
            'fee_buy_rate': float(params['fee_buy_rate']),
            'fee_sell_rate': float(params['fee_sell_rate']),
            'score_sell': float(params['score_sell']),
            'score_buy': float(params['score_buy']),
            'gamma': float(params['gamma']),
            'min_ratio_deal_nav': float(params['min_ratio_deal_nav']),
            'verbose': False,
        }

        # Build score config
        score_config = {
            'calibrate_kind': params['calibrate_kind'],
            'use_temperature': params['use_temperature'],
            'temp_mode': params['temp_mode'],
            'step_round': float(params['step_round']),
            'clip_range': [float(params['clip_range_low']), float(params['clip_range_high'])],
            'score_sell': float(params['score_sell']),  # Add score_sell to score_config
            'score_buy': float(params['score_buy']),    # Add score_buy to score_config
            'fa_flag': False,  # Disable fundamental analysis due to missing columns
        }

        return simulation_config, score_config

    def objective(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Objective function for hyperopt optimization.

        Objective: Maximize combination of CAGR and win_rate_deals.

        Args:
            params: Parameters from hyperopt

        Returns:
            Dict with loss and status for hyperopt
        """
        try:
            # Convert params to configs
            simulation_config, score_config = self._params_to_configs(params)

            # Create simulation instance
            simulator = Simulation_v2(
                simulate_config=simulation_config,
                score_config=score_config,
                start_date=self.start_date,
                end_date=self.end_date,
                num_proc=1,  # Single process for hyperopt worker
                fpath='ticker_v1a'
            )

            # Fix score manager to use 'score' column instead of 'proba'
            simulator.score_manager.proba_col = 'score'

            # Run simulation
            results = simulator.run_fast(self.data, iterate=self.num_iterations)

            # Extract key metrics
            cagr = results.get('CAGR', 0.0)
            win_rate_deals = results.get('win_rate_deals', 0.0)
            max_drawdown = abs(results.get('max_drawdown', -1.0))
            sharpe = results.get('Sharpe', 0.0)
            n_deals = results.get('n_deals', 0)

            # Penalty system for risk management
            penalty = 1.0
            if n_deals < 10:
                penalty *= 0.1  # Heavy penalty for too few deals
            if max_drawdown > 0.5:  # More than 50% drawdown
                penalty *= (0.5 / max_drawdown)
            if sharpe < -1.0:  # Very poor risk-adjusted returns
                penalty *= 0.5

            # Combined objective: 70% CAGR + 30% win_rate_deals
            # Convert to percentage and apply penalty
            objective_value = (0.7 * cagr * 100 + 0.3 * win_rate_deals * 100) * penalty

            # Log results
            result_log = {
                'params': params,
                'CAGR': cagr,
                'win_rate_deals': win_rate_deals,
                'max_drawdown': max_drawdown,
                'sharpe': sharpe,
                'n_deals': n_deals,
                'penalty': penalty,
                'objective_value': objective_value,
                'timestamp': datetime.now().isoformat()
            }

            print(f"Results: CAGR={cagr:.4f}, win_rate={win_rate_deals:.4f}, "
                  f"objective={objective_value:.4f}, n_deals={n_deals}")

            # Save detailed results
            self._save_trial_result(result_log)

            # Return for hyperopt (minimize, so negate objective)
            return {
                'loss': -objective_value,
                'status': STATUS_OK,
                'eval_time': datetime.now().isoformat(),
                'CAGR': cagr,
                'win_rate_deals': win_rate_deals,
                'n_deals': n_deals,
                'objective_value': objective_value
            }

        except Exception as e:
            print(f"Error in objective function: {e}")
            traceback.print_exc()
            return {
                'loss': np.inf,
                'status': STATUS_FAIL,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def _save_trial_result(self, result: Dict[str, Any]):
        """Save detailed trial result to file."""
        try:
            results_file = os.path.join(BASE_DIR, 'tuning/simulation_v2/logs', 'trial_results.jsonl')
            with open(results_file, 'a') as f:
                f.write(json.dumps(result) + '\n')
        except Exception as e:
            print(f"Warning: Could not save trial result: {e}")

    def tune_simulation(self, max_evals: int = 100, mongo_url: str = None) -> Dict[str, Any]:
        """
        Run hyperparameter tuning for simulation.

        Args:
            max_evals: Maximum number of evaluations
            mongo_url: MongoDB URL for distributed tuning

        Returns:
            Best parameters and results
        """
        try:
            # Setup MongoDB trials
            if mongo_url is None:
                mongo_url = f"mongo://{MONGO_HOST}:{MONGO_PORT}/{MONGO_DB}/jobs"

            trials = MongoTrials(mongo_url, exp_key=EXPERIMENT_NAME)

            print(f"Starting hyperparameter tuning with {max_evals} evaluations")
            print(f"MongoDB URL: {mongo_url}")
            print(f"Experiment: {EXPERIMENT_NAME}")

            # Run optimization
            best = fmin(
                fn=partial(safe_objective_wrapper, self.objective),
                space=self.search_space,
                algo=tpe.suggest,
                max_evals=max_evals,
                trials=trials,
                verbose=True
            )

            print(f"Optimization completed. Best parameters: {best}")

            # Save best results
            best_result = {
                'best_params': best,
                'best_loss': trials.best_trial['result']['loss'],
                'n_trials': len(trials.trials),
                'experiment_name': EXPERIMENT_NAME,
                'timestamp': datetime.now().isoformat()
            }

            best_file = os.path.join(BASE_DIR, 'tuning/simulation_v2/logs', 'best_results.json')
            with open(best_file, 'w') as f:
                json.dump(best_result, f, indent=2)

            return best_result

        except Exception as e:
            print(f"Error in tuning: {e}")
            traceback.print_exc()
            raise

    def test_objective(self) -> Dict[str, Any]:
        """Test objective function with default parameters."""
        print("Testing objective function with default parameters...")
        result = self.objective(self.default_params)
        print(f"Test result: {result}")
        return result


# Utility functions for distributed tuning
def run_worker(mongo_url: str = None, max_evals: int = 1000):
    """
    Run a hyperopt worker process.

    Args:
        mongo_url: MongoDB URL for distributed tuning
        max_evals: Maximum evaluations for this worker
    """
    try:
        tuner = SimulationV2Tuner()

        if mongo_url is None:
            mongo_url = f"mongo://{MONGO_HOST}:{MONGO_PORT}/{MONGO_DB}/jobs"

        trials = MongoTrials(mongo_url, exp_key=EXPERIMENT_NAME)

        print(f"Starting worker with MongoDB: {mongo_url}")
        print(f"Experiment: {EXPERIMENT_NAME}")

        # Run worker
        best = fmin(
            fn=partial(safe_objective_wrapper, tuner.objective),
            space=tuner.search_space,
            algo=tpe.suggest,
            max_evals=max_evals,
            trials=trials,
            verbose=True
        )

        print(f"Worker completed. Best found: {best}")

    except Exception as e:
        print(f"Worker error: {e}")
        traceback.print_exc()


def parse_hyperopt_results(mongo_url: str = None, output_file: str = None):
    """
    Parse and analyze hyperopt results from MongoDB.

    Args:
        mongo_url: MongoDB URL
        output_file: Output CSV file path
    """
    try:
        if mongo_url is None:
            mongo_url = f"mongo://{MONGO_HOST}:{MONGO_PORT}/{MONGO_DB}/jobs"

        trials = MongoTrials(mongo_url, exp_key=EXPERIMENT_NAME)

        # Convert trials to DataFrame
        results = []
        for trial in trials.trials:
            if trial['state'] == 2:  # DONE
                result = trial['result']
                params = trial['misc']['vals']

                # Flatten params
                flat_params = {}
                for key, value in params.items():
                    if isinstance(value, list) and len(value) > 0:
                        flat_params[key] = value[0]
                    else:
                        flat_params[key] = value

                # Combine with results
                row = {**flat_params, **result}
                row['trial_id'] = trial['tid']
                results.append(row)

        df = pd.DataFrame(results)

        if output_file is None:
            output_file = os.path.join(BASE_DIR, 'tuning/simulation_v2/logs',
                                     f'results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv')

        df.to_csv(output_file, index=False)
        print(f"Results saved to: {output_file}")
        print(f"Total trials: {len(df)}")

        if len(df) > 0:
            print(f"Best objective: {df['objective_value'].max():.4f}")
            print(f"Best CAGR: {df['CAGR'].max():.4f}")
            print(f"Best win_rate: {df['win_rate_deals'].max():.4f}")

        return df

    except Exception as e:
        print(f"Error parsing results: {e}")
        traceback.print_exc()
        return None

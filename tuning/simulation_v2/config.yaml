# Configuration file for Simulation_v2 hyperparameter tuning
# Author: Augment Agent
# Date: 2025-01-05

# MongoDB Configuration
mongodb:
  host: "localhost"
  port: 27017
  database: "hyperopt_simulation_v2_db"
  experiment_name: "simulation_v2_tuning"

# Data Configuration
data:
  path: "webui/score_v1.csv"
  start_date: "2022-06-01"
  end_date: "2026-01-01"
  required_columns:
    - "time"
    - "ticker"
    - "close"
    - "price"
    - "score"
    - "volume"
    - "volume_1m_p50"

# Simulation Configuration
simulation:
  num_iterations: 10  # Number of iterations for run_fast()
  num_processes: 1    # Processes per worker (keep 1 for hyperopt)
  
# Tuning Configuration
tuning:
  max_evals: 100
  algorithm: "tpe"  # Tree-structured Parzen Estimator
  
  # Objective function weights
  objective_weights:
    cagr: 0.7
    win_rate_deals: 0.3
  
  # Penalty system
  penalties:
    min_deals: 10
    max_drawdown: 0.5
    min_sharpe: -1.0
    
# Search Space Configuration
search_space:
  # Simulation parameters
  simulation_params:
    cutloss:
      type: "quniform"
      low: 0.1
      high: 0.3
      step: 0.02
      
    cutloss_duration:
      type: "quniform"
      low: 5
      high: 30
      step: 5
      
    ratio_nav:
      type: "quniform"
      low: 0.8
      high: 1.0
      step: 0.05
      
    ratio_deal:
      type: "quniform"
      low: 0.05
      high: 0.2
      step: 0.01
      
    ratio_deal_volume:
      type: "quniform"
      low: 0.05
      high: 0.2
      step: 0.01
      
    review_frequency:
      type: "choice"
      options: ["daily", "weekly", "monthly"]
      
    fee_buy_rate:
      type: "quniform"
      low: 0.0005
      high: 0.002
      step: 0.0001
      
    fee_sell_rate:
      type: "quniform"
      low: 0.001
      high: 0.003
      step: 0.0001
      
    score_sell:
      type: "quniform"
      low: -0.5
      high: 1.0
      step: 0.1
      
    score_buy:
      type: "quniform"
      low: 0.5
      high: 2.0
      step: 0.1
      
    gamma:
      type: "quniform"
      low: 0.5
      high: 2.0
      step: 0.1
      
    min_ratio_deal_nav:
      type: "quniform"
      low: 0.005
      high: 0.02
      step: 0.001
  
  # Score parameters
  score_params:
    calibrate_kind:
      type: "choice"
      options: [null, "auto", "iso", "platt"]
      
    use_temperature:
      type: "choice"
      options: [true, false]
      
    temp_mode:
      type: "choice"
      options: ["brier", "none"]
      
    step_round:
      type: "quniform"
      low: 0.1
      high: 0.5
      step: 0.05
      
    clip_range_low:
      type: "quniform"
      low: -2.0
      high: 0.0
      step: 0.2
      
    clip_range_high:
      type: "quniform"
      low: 2.0
      high: 4.0
      step: 0.2

# Default Parameters
defaults:
  simulation:
    cutloss: 0.2
    cutloss_duration: 15
    ratio_nav: 1.0
    ratio_deal: 0.1
    ratio_deal_volume: 0.1
    review_frequency: "monthly"
    fee_buy_rate: 0.001
    fee_sell_rate: 0.002
    score_sell: 0.0
    score_buy: 1.0
    gamma: 1.0
    min_ratio_deal_nav: 0.01
    
  score:
    calibrate_kind: null
    use_temperature: false
    temp_mode: "brier"
    step_round: 0.2
    clip_range_low: -1.0
    clip_range_high: 3.0

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(levelname)s - %(message)s"
  file_pattern: "tuning/simulation_v2/logs/tuning_{timestamp}.log"
  save_trials: true
  trial_file: "tuning/simulation_v2/logs/trial_results.jsonl"

# Output Configuration
output:
  results_dir: "tuning/simulation_v2/logs"
  best_results_file: "best_results.json"
  parsed_results_pattern: "results_{timestamp}.csv"

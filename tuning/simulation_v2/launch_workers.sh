#!/bin/bash
# Script to launch multiple parallel workers for Simulation_v2 hyperparameter tuning
# Author: Augment Agent
# Date: 2025-01-05

# Configuration
NUM_WORKERS=4
MAX_EVALS_PER_WORKER=1000
MONGO_URL="mongo://localhost:27017/hyperopt_simulation_v2_db/jobs"
LOG_DIR="tuning/simulation_v2/logs"

# Ensure log directory exists
mkdir -p $LOG_DIR

# Function to launch a worker
launch_worker() {
    local worker_id=$1
    local log_file="$LOG_DIR/worker_${worker_id}_$(date +%Y%m%d_%H%M%S).log"
    
    echo "Launching worker $worker_id with log: $log_file"
    
    # Activate conda environment if needed
    # conda activate ta
    
    # Launch worker in background
    nohup python tuning/simulation_v2/run_tuning.py \
        --worker \
        --max_evals $MAX_EVALS_PER_WORKER \
        --mongo_url "$MONGO_URL" \
        > "$log_file" 2>&1 &
    
    local pid=$!
    echo "Worker $worker_id started with PID: $pid"
    echo $pid > "$LOG_DIR/worker_${worker_id}.pid"
}

# Function to stop all workers
stop_workers() {
    echo "Stopping all workers..."
    for pid_file in $LOG_DIR/worker_*.pid; do
        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            if kill -0 $pid 2>/dev/null; then
                echo "Stopping worker with PID: $pid"
                kill $pid
            fi
            rm "$pid_file"
        fi
    done
}

# Function to check worker status
check_workers() {
    echo "Checking worker status..."
    for pid_file in $LOG_DIR/worker_*.pid; do
        if [ -f "$pid_file" ]; then
            local worker_id=$(basename "$pid_file" .pid | sed 's/worker_//')
            local pid=$(cat "$pid_file")
            if kill -0 $pid 2>/dev/null; then
                echo "Worker $worker_id (PID: $pid) is running"
            else
                echo "Worker $worker_id (PID: $pid) is not running"
                rm "$pid_file"
            fi
        fi
    done
}

# Function to show help
show_help() {
    echo "Usage: $0 [start|stop|status|restart|help]"
    echo ""
    echo "Commands:"
    echo "  start   - Launch all workers"
    echo "  stop    - Stop all workers"
    echo "  status  - Check worker status"
    echo "  restart - Stop and start all workers"
    echo "  help    - Show this help message"
    echo ""
    echo "Configuration:"
    echo "  NUM_WORKERS: $NUM_WORKERS"
    echo "  MAX_EVALS_PER_WORKER: $MAX_EVALS_PER_WORKER"
    echo "  MONGO_URL: $MONGO_URL"
    echo "  LOG_DIR: $LOG_DIR"
}

# Main script logic
case "${1:-start}" in
    start)
        echo "Starting $NUM_WORKERS workers for Simulation_v2 hyperparameter tuning..."
        echo "MongoDB URL: $MONGO_URL"
        echo "Max evaluations per worker: $MAX_EVALS_PER_WORKER"
        echo "Log directory: $LOG_DIR"
        echo ""
        
        # Check if MongoDB is running
        if ! nc -z localhost 27017; then
            echo "Warning: MongoDB does not appear to be running on localhost:27017"
            echo "Please start MongoDB before launching workers:"
            echo "  mongod --dbpath /path/to/db --port 27017"
            exit 1
        fi
        
        # Launch workers
        for i in $(seq 1 $NUM_WORKERS); do
            launch_worker $i
            sleep 2  # Small delay between launches
        done
        
        echo ""
        echo "All workers launched. Use '$0 status' to check status."
        echo "Use '$0 stop' to stop all workers."
        ;;
        
    stop)
        stop_workers
        ;;
        
    status)
        check_workers
        ;;
        
    restart)
        echo "Restarting workers..."
        stop_workers
        sleep 3
        $0 start
        ;;
        
    help)
        show_help
        ;;
        
    *)
        echo "Unknown command: $1"
        show_help
        exit 1
        ;;
esac

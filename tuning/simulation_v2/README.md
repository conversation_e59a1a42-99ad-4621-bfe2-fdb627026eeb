# Simulation_v2 Hyperparameter Tuning

Hệ thống hyperparameter tuning cho `Simulation_v2` sử dụng Hyperopt với MongoDB parallelization.

## 🎯 Mục tiêu

Tối ưu hóa các parameters trong `score_config` và `simulate_config` để maximize:
- **CAGR** (Compound Annual Growth Rate): 70% weight
- **win_rate_deals** (Tỷ lệ thắng của deals): 30% weight

## 🏗️ Kiến trúc

```
tuning/simulation_v2/
├── hyo_tuning_manager.py    # Core tuning logic
├── run_tuning.py           # Main execution script
├── launch_workers.sh       # Parallel worker launcher
├── config.yaml            # Configuration file
├── logs/                   # Logs và results
└── README.md              # Documentation
```

## 🚀 Quick Start

### 1. Chuẩn bị môi trường

```bash
# Activate conda environment
conda activate ta

# Start MongoDB
mongod --dbpath /path/to/db --port 27017
```

### 2. Test objective function

```bash
python tuning/simulation_v2/run_tuning.py --test
```

### 3. Chạy single tuning process

```bash
python tuning/simulation_v2/run_tuning.py --tune --max_evals 100
```

### 4. Chạy parallel workers

```bash
# Launch multiple workers
./tuning/simulation_v2/launch_workers.sh start

# Check worker status
./tuning/simulation_v2/launch_workers.sh status

# Stop all workers
./tuning/simulation_v2/launch_workers.sh stop
```

### 5. Parse và analyze results

```bash
python tuning/simulation_v2/run_tuning.py --parse
```

## ⚙️ Configuration

### Search Space

**Simulation Parameters:**
- `cutloss`: 10% - 30% (step 2%)
- `cutloss_duration`: 5-30 days (step 5)
- `ratio_nav`: 80% - 100% (step 5%)
- `ratio_deal`: 5% - 20% (step 1%)
- `fee_buy_rate`: 0.05% - 0.2%
- `fee_sell_rate`: 0.1% - 0.3%
- `score_sell`: -0.5 to 1.0 (step 0.1)
- `score_buy`: 0.5 to 2.0 (step 0.1)

**Score Parameters:**
- `calibrate_kind`: [None, 'auto', 'iso', 'platt']
- `use_temperature`: [True, False]
- `step_round`: 0.1 to 0.5 (step 0.05)
- `clip_range`: Low [-2.0, 0.0], High [2.0, 4.0]

### Objective Function

```python
# Combined objective với penalty system
objective_value = (0.7 * CAGR * 100 + 0.3 * win_rate_deals * 100) * penalty

# Penalty conditions:
- n_deals < 10: penalty *= 0.1
- max_drawdown > 50%: penalty *= (0.5 / max_drawdown)  
- sharpe < -1.0: penalty *= 0.5
```

## 📊 Monitoring

### Logs

- **Worker logs**: `tuning/simulation_v2/logs/worker_*.log`
- **Trial results**: `tuning/simulation_v2/logs/trial_results.jsonl`
- **Best results**: `tuning/simulation_v2/logs/best_results.json`

### MongoDB

```bash
# Connect to MongoDB
mongo localhost:27017/hyperopt_simulation_v2_db

# Check experiment status
db.jobs.find({"exp_key": "simulation_v2_tuning"}).count()
```

## 🔧 Advanced Usage

### Custom data path

```bash
python tuning/simulation_v2/run_tuning.py --tune \
    --data_path "path/to/custom/data.csv" \
    --start_date "2023-01-01" \
    --end_date "2025-12-31"
```

### Custom MongoDB URL

```bash
python tuning/simulation_v2/run_tuning.py --worker \
    --mongo_url "mongo://remote-host:27017/custom_db/jobs"
```

### Modify search space

Edit `config.yaml` hoặc modify `_define_search_space()` trong `hyo_tuning_manager.py`.

## 📈 Results Analysis

### Best parameters

```python
import json
with open('tuning/simulation_v2/logs/best_results.json') as f:
    best = json.load(f)
print(best['best_params'])
```

### Trial analysis

```python
import pandas as pd
df = pd.read_csv('tuning/simulation_v2/logs/results_*.csv')

# Top 10 trials
top_trials = df.nlargest(10, 'objective_value')
print(top_trials[['CAGR', 'win_rate_deals', 'objective_value']])

# Parameter correlation
import seaborn as sns
sns.heatmap(df.corr())
```

## 🚨 Troubleshooting

### MongoDB connection issues

```bash
# Check MongoDB status
systemctl status mongod

# Check port
netstat -tlnp | grep 27017
```

### Memory issues

- Reduce `num_iterations` in config
- Reduce `NUM_WORKERS` in launch script
- Monitor với `htop`

### Data loading errors

- Check data path và required columns
- Verify date format consistency
- Check for missing values

## 🔄 Time-based Validation

Hệ thống đảm bảo **no look-ahead bias**:
- Train/validation split theo thời gian
- Simulation chỉ sử dụng data đến thời điểm t
- Proper time-series cross-validation

## 📝 Notes

- **Step sizes**: Được thiết kế để tránh overfitting
- **Penalty system**: Đảm bảo practical trading constraints
- **Parallelization**: MongoDB backend cho distributed tuning
- **Reproducibility**: Random seeds và logging đầy đủ

## 🤝 Contributing

Khi modify code:
1. Test với `--test` flag trước
2. Update documentation
3. Maintain backward compatibility
4. Follow PEP8 standards

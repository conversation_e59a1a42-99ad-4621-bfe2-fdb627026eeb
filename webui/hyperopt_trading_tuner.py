"""
Hyperparameter Tuning cho Trading Strategy sử dụng Hyperopt với MongoDB Backend

Tối ưu hóa các tham số trong simulation_config và score_config để maximize CAGR và win_rate_deals.

Usage:
1. Start MongoDB: mongod --dbpath /path/to/db --port 27017
2. Run multiple workers: python hyperopt_trading_tuner.py --worker
3. Run main tuning: python hyperopt_trading_tuner.py --tune --max_evals 1000

Author: Augment Agent
Date: 2025-01-05
"""

import os
import sys
import json
import logging
import argparse
import warnings
from datetime import datetime, timedelta
from typing import Dict, Any, Tuple
import traceback

import numpy as np
import pandas as pd
from hyperopt import hp, fmin, tpe, STATUS_OK, STATUS_FAIL
from hyperopt.mongoexp import MongoTrials
from pymongo import MongoClient
import joblib

# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from core_utils.simulation_v2a import WrapperSimulation
from core_utils.warp_score import ScoreManager

warnings.filterwarnings('ignore')

# Configuration
MONGO_HOST = "localhost"
MONGO_PORT = 27017
MONGO_DB = "hyperopt_trading_db"
EXPERIMENT_NAME = "trading_strategy_v1"

# Logging setup
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'webui/logs/hyperopt_tuning_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Ensure logs directory exists
os.makedirs('webui/logs', exist_ok=True)
os.makedirs('webui/results', exist_ok=True)


class TradingStrategyTuner:
    """
    Hyperparameter tuner cho trading strategy sử dụng hyperopt với MongoDB backend.
    
    Tối ưu hóa kết hợp CAGR và win_rate_deals từ simulation results.
    """
    
    def __init__(self, data_path: str = 'webui/score_v1.csv'):
        """
        Initialize tuner.
        
        Args:
            data_path: Path to score data CSV file
        """
        self.data_path = data_path
        self.data = None
        self.search_space = self._define_search_space()
        self.default_params = self._get_default_params()
        
        # Load and prepare data
        self._load_data()
        
    def _load_data(self):
        """Load and prepare data for simulation."""
        try:
            logger.info(f"Loading data from {self.data_path}")
            
            # Load data with required columns
            required_cols = ['time', 'ticker', 'close', 'price', 'score', 'volume', 'volume_p50_1m']
            self.data = pd.read_csv(self.data_path, usecols=required_cols)
            
            # Data preprocessing
            self.data['time'] = pd.to_datetime(self.data['time'])
            self.data = self.data.dropna()
            
            # Filter valid data
            valid_mask = (
                (self.data['close'] > 0) & 
                (self.data['price'] > 0) & 
                (self.data['volume_p50_1m'] > 0) & 
                (self.data['score'].notna()) &
                (self.data['time'] >= '2014-01-01')
            )
            self.data = self.data[valid_mask].copy()
            
            # Prepare for simulation
            self.data['ymd'] = self.data['time'].dt.strftime('%Y-%m-%d')
            self.data = self.data.rename(columns={
                'close': 'close_price',
                'volume_p50_1m': 'volume_1m_p50'
            })
            self.data['open_price'] = self.data['close_price']  # Simplified assumption
            self.data['daily_amount'] = self.data['volume_1m_p50'] * self.data['price']
            
            logger.info(f"Data loaded successfully: {len(self.data)} rows, "
                       f"Date range: {self.data['time'].min()} to {self.data['time'].max()}")
            
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            raise
    
    def _define_search_space(self) -> Dict[str, Any]:
        """
        Define hyperopt search space cho cả simulation_config và score_config.
        
        Returns:
            Dictionary containing hyperopt search space definitions
        """
        return {
            # === Simulation Config Parameters ===
            'cutloss': hp.uniform('cutloss', 0.1, 0.3),  # 10% - 30%
            'cutloss_duration': hp.quniform('cutloss_duration', 5, 30, 5),  # 5-30 days, step 5
            'ratio_nav': hp.uniform('ratio_nav', 0.8, 1.0),  # 80% - 100%
            'ratio_deal': hp.uniform('ratio_deal', 0.05, 0.2),  # 5% - 20%
            'ratio_deal_volume': hp.uniform('ratio_deal_volume', 0.05, 0.2),  # 5% - 20%
            'review_frequency': hp.choice('review_frequency', ['daily', 'weekly', 'monthly']),
            'fee_buy_rate': hp.uniform('fee_buy_rate', 0.0005, 0.002),  # 0.05% - 0.2%
            'fee_sell_rate': hp.uniform('fee_sell_rate', 0.001, 0.003),  # 0.1% - 0.3%
            'score_sell': hp.uniform('score_sell', -0.5, 1.0),  # -0.5 to 1.0
            'score_buy': hp.uniform('score_buy', 0.5, 2.0),  # 0.5 to 2.0
            'gamma': hp.uniform('gamma', 0.5, 2.0),  # 0.5 to 2.0
            'min_ratio_deal_nav': hp.uniform('min_ratio_deal_nav', 0.005, 0.02),  # 0.5% - 2%
            
            # === Score Config Parameters ===
            'calibrate_kind': hp.choice('calibrate_kind', [None, 'auto', 'iso', 'platt', 'identity']),
            'use_temperature': hp.choice('use_temperature', [True, False]),
            'temp_mode': hp.choice('temp_mode', ['brier', 'none']),
            'step_round': hp.uniform('step_round', 0.1, 0.5),  # 0.1 to 0.5
            'clip_range_low': hp.uniform('clip_range_low', -2.0, 0.0),  # -2.0 to 0.0
            'clip_range_high': hp.uniform('clip_range_high', 2.0, 4.0),  # 2.0 to 4.0
        }
    
    def _get_default_params(self) -> Dict[str, Any]:
        """Get default parameter values for initial testing."""
        return {
            # Simulation defaults
            'cutloss': 0.2,
            'cutloss_duration': 15,
            'ratio_nav': 1.0,
            'ratio_deal': 0.1,
            'ratio_deal_volume': 0.1,
            'review_frequency': 'monthly',
            'fee_buy_rate': 0.001,
            'fee_sell_rate': 0.002,
            'score_sell': 0.0,
            'score_buy': 1.0,
            'gamma': 1.0,
            'min_ratio_deal_nav': 0.01,
            
            # Score defaults
            'calibrate_kind': None,
            'use_temperature': False,
            'temp_mode': 'brier',
            'step_round': 0.2,
            'clip_range_low': -1.0,
            'clip_range_high': 3.0,
        }

    def _params_to_configs(self, params: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Convert hyperopt params to simulation_config and score_config.

        Args:
            params: Parameters from hyperopt

        Returns:
            Tuple of (simulation_config, score_config)
        """
        # Build simulation config
        simulation_config = {
            'initial_amount': 1e9,  # Fixed
            'cutloss': float(params['cutloss']),
            'cutloss_duration': int(params['cutloss_duration']),
            'ratio_nav': float(params['ratio_nav']),
            'ratio_deal': float(params['ratio_deal']),
            'ratio_deal_volume': float(params['ratio_deal_volume']),
            'review_frequency': params['review_frequency'],
            'fee_buy_rate': float(params['fee_buy_rate']),
            'fee_sell_rate': float(params['fee_sell_rate']),
            'score_sell': float(params['score_sell']),
            'score_buy': float(params['score_buy']),
            'gamma': float(params['gamma']),
            'min_ratio_deal_nav': float(params['min_ratio_deal_nav']),
            'verbose': False,
        }

        # Build score config
        score_config = {
            'score_col': 'score',
            'proba_col': 'proba',
            'calibrate_kind': params['calibrate_kind'],
            'use_temperature': params['use_temperature'],
            'temp_mode': params['temp_mode'],
            'percentiles': (0, 20, 40, 60, 80, 100),  # Fixed
            'score_knots': None,  # Fixed
            'lift_target_rates': None,  # Fixed
            'base_rate': None,  # Fixed
            'clip_range': (float(params['clip_range_low']), float(params['clip_range_high'])),
            'step_round': float(params['step_round']),
        }

        return simulation_config, score_config

    def objective(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Objective function cho hyperopt optimization.

        Mục tiêu: Maximize kết hợp của CAGR và win_rate_deals.

        Args:
            params: Parameters from hyperopt

        Returns:
            Dictionary with loss and status for hyperopt
        """
        try:
            logger.info(f"Evaluating params: {params}")

            # Convert params to configs
            simulation_config, score_config = self._params_to_configs(params)

            # Prepare data with scores
            df_scored = self.data.copy()

            # Apply score transformation if needed
            if any([score_config['calibrate_kind'], score_config['use_temperature'],
                   score_config['step_round'] != 0.2]):
                try:
                    score_manager = ScoreManager(score_config)
                    df_scored = score_manager.run(df_scored)
                except Exception as e:
                    logger.warning(f"Score transformation failed: {e}, using original scores")

            # Run simulation
            sim = WrapperSimulation(simulation_config)
            sim.run(df_scored)

            # Get results
            stats = sim.stats()
            stats_log = stats['stats_log']
            stats_tx = stats['stats_tx']['tmetrics']

            # Extract key metrics
            cagr = stats_log.get('CAGR', 0.0)
            win_rate_deals = stats_tx.get('win_rate_deals', 0.0)
            max_drawdown = abs(stats_log.get('max_drawdown', -1.0))
            sharpe = stats_log.get('Sharpe', 0.0)
            n_deals = stats_tx.get('n_deals', 0)

            # Penalty for insufficient deals or extreme drawdown
            penalty = 1.0
            if n_deals < 10:
                penalty *= 0.1  # Heavy penalty for too few deals
            if max_drawdown > 0.5:  # More than 50% drawdown
                penalty *= (0.5 / max_drawdown)

            # Combined objective: 70% CAGR + 30% win_rate_deals
            # Convert to percentage and apply penalty
            objective_value = (0.7 * cagr * 100 + 0.3 * win_rate_deals * 100) * penalty

            # Log results
            result_log = {
                'params': params,
                'CAGR': cagr,
                'win_rate_deals': win_rate_deals,
                'max_drawdown': max_drawdown,
                'sharpe': sharpe,
                'n_deals': n_deals,
                'penalty': penalty,
                'objective_value': objective_value,
                'timestamp': datetime.now().isoformat()
            }

            logger.info(f"Results: CAGR={cagr:.4f}, win_rate={win_rate_deals:.4f}, "
                       f"objective={objective_value:.4f}, n_deals={n_deals}")

            # Save detailed results
            self._save_trial_result(result_log)

            # Return negative because hyperopt minimizes
            return {
                'loss': -objective_value,
                'status': STATUS_OK,
                'eval_time': datetime.now().isoformat(),
                'CAGR': cagr,
                'win_rate_deals': win_rate_deals,
                'n_deals': n_deals,
                'objective_value': objective_value
            }

        except Exception as e:
            logger.error(f"Error in objective function: {e}")
            logger.error(traceback.format_exc())
            return {
                'loss': 1e6,  # Large penalty for failed trials
                'status': STATUS_FAIL,
                'error': str(e)
            }

    def _save_trial_result(self, result: Dict[str, Any]):
        """Save trial result to JSON file."""
        try:
            results_file = f'webui/results/trial_results_{EXPERIMENT_NAME}.jsonl'
            with open(results_file, 'a') as f:
                f.write(json.dumps(result) + '\n')
        except Exception as e:
            logger.warning(f"Failed to save trial result: {e}")

    def cleanup_stuck_jobs(self):
        """Clean up stuck MongoDB jobs."""
        try:
            client = MongoClient(f"mongodb://{MONGO_HOST}:{MONGO_PORT}")
            collection = client[MONGO_DB]["jobs"]

            # Remove stuck jobs
            query_broken_jobs = {
                '$or': [
                    {'result.status': 'new'},
                    {'spec': None},
                    {'spec': {}}
                ]
            }
            result = collection.delete_many(query_broken_jobs)
            logger.info(f"Cleaned up {result.deleted_count} stuck jobs")

        except Exception as e:
            logger.warning(f"Failed to cleanup stuck jobs: {e}")

    def tune(self, max_evals: int = 1000) -> Dict[str, Any]:
        """
        Run hyperparameter tuning.

        Args:
            max_evals: Maximum number of evaluations

        Returns:
            Best parameters found
        """
        logger.info(f"Starting hyperparameter tuning with max_evals={max_evals}")

        # Cleanup and setup
        self.cleanup_stuck_jobs()

        # Test with default parameters first
        logger.info("Testing with default parameters...")
        default_result = self.objective(self.default_params)
        logger.info(f"Default result: {default_result}")

        # Setup MongoDB trials
        trials_name = f"{EXPERIMENT_NAME}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        mongo_url = f'mongo://{MONGO_HOST}:{MONGO_PORT}/{MONGO_DB}/jobs'
        trials = MongoTrials(mongo_url, exp_key=trials_name)

        logger.info(f"Using MongoDB trials: {mongo_url}, exp_key: {trials_name}")

        # Run optimization
        best = fmin(
            fn=self.objective,
            space=self.search_space,
            algo=tpe.suggest,
            max_evals=max_evals,
            trials=trials,
            rstate=np.random.default_rng(42)
        )

        logger.info(f"Optimization completed. Best parameters: {best}")

        # Save best parameters
        best_result = {
            'best_params': best,
            'experiment_name': trials_name,
            'max_evals': max_evals,
            'total_trials': len(trials.trials),
            'timestamp': datetime.now().isoformat()
        }

        best_file = f'webui/results/best_params_{EXPERIMENT_NAME}.json'
        with open(best_file, 'w') as f:
            json.dump(best_result, f, indent=2)

        logger.info(f"Best parameters saved to {best_file}")

        return best

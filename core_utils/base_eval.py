import json
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor
from datetime import datetime
from datetime import timedelta
from functools import wraps, lru_cache

import numpy as np
import pandas as pd
import os
from joblib import Memory

from core_utils.constant import RANKING, MAX_DECREASE, SIMULATE_FULL_CASH, SIMULATE_FULL_CASH_NOT_FIX, \
    SIMULATE_ALLOCATE, JOBLIB_CACHE_DIR
from core_utils.cache_provider import get_cache_singleton, NullCache

memory = Memory(location=JOBLIB_CACHE_DIR, verbose=0)
try:
    memory.reduce_size(bytes_limit=3e9, age_limit=timedelta(days=1))
except TypeError:
    # Fallback for older joblib versions - just skip cache reduction
    pass

class BaseEval:
    """
    Evaluate filters for a given ticker
    """

    def __init__(self, stock, data_frame, dict_filter, df_market=None, cutloss=0.15, cache_service=None):
        """
        Initialize the TickerEval class.

        Args:
            stock (str): The stock ticker symbol.
            data_frame (pd.DataFrame): The data frame containing the stock data.
            dict_filter (dict): A dictionary of filters to apply to the data.
            cutloss (float, optional): The cut loss threshold. Defaults to 0.15.
        """
        self.ticker = stock
        self.df_all = data_frame
        self.dictFilter, self.b_f, self.s_f, self.pre_f = self.procress_filter(dict_filter)
        self.cutloss = cutloss
        self.redis_cache = cache_service or get_cache_singleton() or NullCache()

        self.buy_2_sell = self.buy_map_sell()

        self.df_sell = self.sell_signals()
        self.df_buy = self.buy_signals()

        self.df_market = df_market

    @staticmethod
    def open_df(path, ticker):
        try:
            return pd.read_csv(f'{path}/{ticker}.csv')
        except FileNotFoundError:
            return None

    @staticmethod
    def cache_prefix_result(ticker_name=True, param=False, unique=False, expiry=None):
        """Decorator to cache the result of a method with an optional expiry parameter."""

        def decorator(method):
            @wraps(method)
            def wrapper(self, *args, **kwargs):
                # Generate a unique cache key based on method name, arguments, and keyword arguments
                # key_base = f"{self.__class__.__name__}:{method.__name__}:{args}:{kwargs}"
                method_name = method.__name__
                # print(method_name)
                if method_name == "sell_signals":
                    key_base = f"{json.dumps(self.s_f, sort_keys=True)}"
                elif method_name == 'buy_signals':
                    key_base = f"{json.dumps(self.b_f, sort_keys=True)}:{json.dumps(self.pre_f, sort_keys=True)}"
                # elif method_name == "pd_query":
                #     key_base = f"{args}:{kwargs}"
                #     method_name = f"pd_query:{args[0]}"
                elif param:
                    key_base = f"{args}:{kwargs}"
                    # print(key_base)
                else:
                    key_base = f"{json.dumps(self.dictFilter, sort_keys=True)}"

                if ticker_name:
                    key_base = f"{self.ticker}:{key_base}"

                key_base = f"{self.__class__.__name__}:{key_base}"

                cache_key = self.redis_cache.generate_cache_key(key_base)
                cached_result = self.redis_cache.get_with_expiry_update(prefix=method_name, key=cache_key)
                # Attempt to retrieve from cache
                if cached_result is not None:
                    # print("cache hit")
                    return cached_result

                # Manange unique cache
                if unique:
                    self.redis_cache.manage_prefix_unique_cache(prefix=method_name, r_name=self.ticker, key=cache_key)

                # Call the method and cache its result
                result = method(self, *args, **kwargs)
                self.redis_cache.set_prefix_cache(prefix=method_name, key=cache_key, value=result, expiry=expiry)

                return result

            return wrapper

        return decorator

    @staticmethod
    def procress_filter(dict_filter: dict):
        sell_filter = {}
        buy_filter = {}
        d_filter = {}
        presell_filter = {}
        for k, v in dict_filter.items():
            value = v.strip()
            d_filter[k] = value
            if k.startswith('_'):
                buy_filter[k] = value
            if k.startswith('~'):
                sell_filter[k] = value
            if k.startswith('#'):
                presell_filter = value

        # return {
        #     'full': d_filter,
        #     'b_f':  buy_filter,
        #     's_f': sell_filter,
        #     'pre_f': presell_filter
        #
        # }
        return d_filter, buy_filter, sell_filter, presell_filter

    @cache_prefix_result(param=True, expiry=600)
    def pd_query(self, f, v):
        f_col = "Sell_filter" if f.startswith("~") else "filter"

        cols = ['time', 'ticker', 'Close', 'Price', 'Open_1D', 'Volume', 'C1W', 'C2W', 'C3W', 'C1M', 'C2M', 'C3M',
                'C6M', 'C1Y', 'C2Y', 'L1W', 'L2W', 'L3W', 'L1M', 'L2M', 'L3M', 'L6M', 'L1Y', 'L2Y', 'H1W', 'H2W',
                'H3W',
                'H1M', 'H2M', 'H3M', 'H6M', 'H1Y', 'H2Y', 'O1W', 'O2W', 'O3W', 'O1M', 'O2M', 'O3M', 'O6M', 'O1Y',
                'O2Y'] + [f_col]

        try:
            result = self.df_all.query(v).copy()
            result[f_col] = f[1:]
            return result[cols]
        except Exception as e:
            # print(f"[{self.ticker}] pd_query fail `{f}` with query `{v}`: {e}")
            return pd.DataFrame(columns=cols)

    @cache_prefix_result(unique=True)
    def sell_signals(self):
        # Apply sell filters
        s_cols = ['ticker', 'time', 'Close', 'Price', 'Open_1D', 'Volume', 'Sell_filter', 'P1W', 'P2W', 'P3W', 'P1M',
                  'P2M', 'P3M', 'P6M', 'P1Y', 'P2Y']

        now = self.df_all.iloc[-1:].copy()
        now['Sell_filter'] = 'Hold'

        sell_data = [now]

        for f in self.dictFilter:
            if f.startswith('~'):
                try:
                    pd_Sell_filtered = self.pd_query(f, self.dictFilter[f])
                    sell_data.append(pd_Sell_filtered)
                except Exception as e:
                    # print(f"{self.ticker}--{f[1:]}--error: {e}")
                    pass

        _sell_data = [data for data in sell_data if not data.empty]

        if _sell_data:
            # for profit for shortsell
            pd_sell = pd.concat(_sell_data, axis=0).sort_values('time', ascending=True)
            for period in ['1W', '2W', '3W', '1M', '2M', '3M', '6M', '1Y', '2Y']:
                pd_sell[f"P{period}"] = 100. * (1 - pd_sell[f"O{period}"]) * (
                        pd_sell[f"H{period}"] < 1 + self.cutloss) + (-100. * self.cutloss) * (
                                                pd_sell[f"H{period}"] >= 1 + self.cutloss)

        else:
            pd_sell = pd.DataFrame(columns=s_cols)

        return pd_sell[s_cols]

    @cache_prefix_result(unique=True)
    def buy_signals(self):
        b_cols = ['time', 'ticker', 'Close', 'Price', 'Open_1D', 'Volume', 'O1W', 'O2W', 'O3W', 'O1M', 'O2M', 'O3M',
                  'O6M', 'O1Y', 'O2Y', 'P1W', 'P2W', 'P3W', 'P1M', 'P2M', 'P3M', 'P6M', 'P1Y', 'P2Y', 'filter', 'hit',
                  'month']

        # Apply buy filters
        buy_data = []

        for f in self.dictFilter:
            if f.startswith('_'):
                try:
                    pd_buy_filtered = self.pd_query(f, self.dictFilter[f])
                    buy_data.append(pd_buy_filtered)
                except Exception as e:
                    # print(f"{self.ticker}--{f[1:]}--error: {e}")
                    pass

        _buy_data = [data for data in buy_data if not data.empty]
        if _buy_data:
            pd_buy = pd.concat(_buy_data, axis=0).sort_values('time', ascending=True)

            for period in ['1W', '2W', '3W', '1M', '2M', '3M', '6M', '1Y', '2Y']:
                pd_buy[f"P{period}"] = 100. * (pd_buy[f"O{period}"] - 1) * (
                        pd_buy[f"L{period}"] > 1 - self.cutloss) + (-100. * self.cutloss) * (
                                               pd_buy[f"L{period}"] <= 1 - self.cutloss)
        else:
            pd_buy = pd.DataFrame(columns=b_cols)

        pd_buy['hit'] = 1
        pd_buy['month'] = pd_buy['time'].map(lambda x: x[:7])

        pd_buy = self._pre_sell(pd_buy)

        return pd_buy

    @cache_prefix_result(ticker_name=False, expiry=300)
    def buy_map_sell(self):
        all_filters = ["Hold"]
        pre_pattern = []
        buy2sell = {}

        for f in self.dictFilter:
            if f.startswith("$"):
                sell_key = f[1:]
                value = [x.replace("~", "").strip() for x in self.dictFilter[f].split(",")]
                value.append("Hold")
                buy2sell[sell_key] = value
            if f.startswith('#'):
                sell_key = f[1:]
                buy2sell[sell_key] = self.dictFilter[f]
                pre_pattern.append(sell_key)

        for f in self.dictFilter:
            if f.startswith('~'):
                all_filters.append(f[1:])
            if f.endswith('~'):
                for k, v in buy2sell.items():
                    if f[1:] not in v and k not in pre_pattern:
                        buy2sell[k].append(f[1:])

        buy2sell['all'] = list(set(all_filters))
        buy2sell['pre_pattern'] = pre_pattern

        return buy2sell

    def get_buy_signals(self):
        cols = ['time', 'ticker', 'Close', 'Price', 'Open_1D', 'Volume', 'filter', 'P1W', 'P2W', 'P3W', 'P1M', 'P2M',
                'P3M', 'P6M', 'P1Y', 'P2Y']
        return self.df_buy[cols]

    def get_sell_signals(self):
        cols = ['time', 'ticker', 'Close', 'Price', 'Open_1D', 'Volume', 'Sell_filter', 'P1W', 'P2W', 'P3W', 'P1M',
                'P2M', 'P3M', 'P6M', 'P1Y', 'P2Y']
        return self.df_sell[cols]

    # Predefine Sell
    def _predefine_sell(self, df_buy: pd.DataFrame) -> pd.DataFrame:
        df_buy = df_buy.copy()
        map_dict = {}
        updated_rows = []
        for idx, row in df_buy.iterrows():
            # get all pre_pattern and find pre_sell
            b_filter = row['filter']

            # Cache matching patterns
            if b_filter not in map_dict:
                map_dict[b_filter] = [f for f in self.buy_2_sell.get(b_filter, [])
                                      if f in self.buy_2_sell.get("pre_pattern", [])]

            pre_pattern = map_dict[b_filter]
            result = {}

            # Dynamically evaluate each pattern logic
            for pattern in pre_pattern:
                func_expr = self.buy_2_sell[pattern]
                command = f"self._{func_expr}".replace(")", f", df_buy=df_buy, index={idx}, pattern='{pattern}')")
                try:
                    result.update(eval(command))
                except Exception as e:
                    print(f"Error evaluating pattern '{pattern}': {e}")

            # if 'exit_under_profit_expected' in self.buy_2_sell["all"]:
            #     id_min = idx + 120
            #     if result:
            #         id_min = min(result.keys())
            #     result.update(self.predefine_exit_under_profit_expected(idx, id_min))

            # update dataframe
            if result:
                sell_id = min(result.keys())
                row["pre_sell"] = result[sell_id].get("pre_sell")
                row["pre_sell_profit"] = result[sell_id].get("pre_sell_profit")
                row["pre_sell_id"] = sell_id

            updated_rows.append(row)

        return pd.DataFrame(updated_rows) if updated_rows else df_buy

    def _force_sell(self, df_buy: pd.DataFrame) -> pd.DataFrame:
        df_buy = df_buy.copy()
        df_sell_filtered = self.df_sell[self.df_sell["Sell_filter"].str.endswith("~", na=False)].sort_values("time")
        results = []
        for _, row in df_buy.iterrows():
            buy_time = row["time"]
            future_sells = df_sell_filtered[df_sell_filtered["time"] > buy_time]

            if not future_sells.empty:
                first_sell = future_sells.iloc[[0]]
                if np.isnan(row['pre_sell_id']) or row['pre_sell_id'] > first_sell.index[0]:
                    row["pre_sell_id"] = first_sell.index[0]
                    row["pre_sell"] = "force_sell"
                    row["pre_sell_profit"] = ((first_sell["Open_1D"] / row["Open_1D"] - 1)).values[0]

            results.append(row)
        return pd.DataFrame(results) if results else df_buy

    def _pre_sell(self, df_buy):
        df_buy = df_buy.copy()
        df_buy[['pre_sell', 'pre_sell_profit', 'pre_sell_id']] = np.nan

        df_buy = self._predefine_sell(df_buy)
        df_buy = self._force_sell(df_buy)
        return df_buy

    def _block_buy_signals(self, df_buy: pd.DataFrame, block_len: int = 30,
                           overheating_block: bool = False) -> pd.DataFrame:
        df_buy = df_buy.copy()
        df_buy["block"] = False
        df_buy = self._block_force_sell(df_buy, block_len=block_len)
        if overheating_block:
            df_buy = self._block_by_index(df_buy, comparison="greater")

        return df_buy

    def _block_force_sell(self, df_buy: pd.DataFrame, block_len: int = 30) -> pd.DataFrame:
        df_buy = df_buy.copy()

        df_sell_filtered = self.df_sell[self.df_sell["Sell_filter"].str.endswith("~", na=False)].sort_values("time")
        df_sell_filtered['month'] = df_sell_filtered['time'].map(lambda x: x[:7])
        df_sell_filtered = df_sell_filtered.drop_duplicates(subset=['month'], keep='first')

        # buy_idx = df_buy.index
        # buy_times = df_buy["time"]

        for _, sell_row in df_sell_filtered.iterrows():
            sell_time = sell_row["time"]
            end_time = (pd.to_datetime(sell_row["time"]) + pd.Timedelta(days=block_len)).strftime('%Y-%m-%d')

            mask = (df_buy["time"] >= sell_time) & (df_buy["time"] < end_time)
            df_buy.loc[mask, "block"] = True

        return df_buy

    def _block_by_index(self, df_buy: pd.DataFrame, comparison="greater") -> pd.DataFrame:
        df_buy = df_buy.copy()
        if self.df_market is None:
            return df_buy

        for idx, row in self.df_market.iterrows():
            if row['type'] != comparison:
                continue

            # Before confirm increase sharply
            start_time = (row['start_group'] + pd.Timedelta(days=90)).strftime('%Y-%m-%d')
            end_time = row['end_group'].strftime('%Y-%m-%d')
            mask = (df_buy["time"] >= start_time) & (df_buy["time"] < end_time)
            df_buy.loc[mask, "block"] = True

        return df_buy

    def _exit_under_profit(self, period, value, df_buy, index, pattern):
        """
        Using for predefine exit
        """
        DICT_SHIFT = {'1W': 5, '2W': 10, '3W': 15, '1M': 20, '2M': 40, '3M': 60, '6M': 120, '1Y': 240, '2Y': 480}

        res = {}
        if (df_buy.loc[index, f'O{period}'] - 1 < value).all():
            key = min(index + DICT_SHIFT[period], self.df_all.shape[0] - 1)
            res[key] = {"pre_sell": pattern,
                        "pre_sell_profit": np.mean(df_buy.loc[index, f'O{period}'] - 1)}
        return res

    def _predefine_exit_under_profit_expected(self, df_buy, index, id_min=1000000):
        w1 = 0.994
        w2 = 0.072
        w3 = -0.341
        res = {}

        duratrion = min(self.df_all.shape[0] - 1 - index, id_min - index)
        start = index + 3
        end = start + duratrion

        df_data = self.df_all[start:end].copy()
        df_data['holding_session'] = list(range(3, 3 + len(df_data)))
        # df_data['label_p1m'] = df_data['Close_1M'] / df_data['Close_y']

        df_data['pred'] = w1 * (df_data['Close'] / df_data['Close_T1M']) + w2 * (df_data['holding_session'] / 30) + w3
        key = df_data[df_data['pred'] < 1.03].index
        if list(key):
            key = min(df_data[df_data['pred'] < 1.03].index)
            res[key] = {"pre_sell": "exit_under_profit_expected",
                        "pre_sell_profit": self.df_all.loc[key, 'Open_1D'] / np.mean(
                            self.df_buy.loc[index, 'Open_1D'] - 1)}

        return res

    # Common Function
    def get_extend_cols(self, df_00, extend_cols, on_col=None):
        if on_col is None:
            on_col = ['time']
        cols = on_col.copy()
        for col in extend_cols:
            if (col not in df_00.columns) and (col in self.df_all.columns):
                cols.append(col)
        df_deal = df_00.merge(self.df_all[cols], on=on_col, how='left')
        return df_deal

    def append_full_indicators(self, df_00):
        cols = ['time']
        for col in self.df_all.columns:
            if col not in df_00.columns:
                cols.append(col)
        df_deal = df_00.merge(self.df_all[cols], on=['time'], how='left')
        df_deal.drop_duplicates(subset=['time', 'filter'], keep='first', inplace=True)
        return df_deal


class AllEval(BaseEval):
    def __init__(self, stock, data_frame, dict_filter, cutloss=0.15, df_market=None, cache_service=None):
        super().__init__(stock=stock, data_frame=data_frame, dict_filter=dict_filter, cutloss=cutloss,
                         df_market=df_market, cache_service=cache_service)

    def eval_by_hit(self):
        b_cols = ['filter', 'ticker', 'hit', 'month', 'time', 'Close', 'Price', 'Open_1D', 'Volume', 'P1W', 'P1M',
                  'P3M', 'P6M', 'P1Y', 'P2Y', 'pre_sell', 'pre_sell_profit', 'pre_sell_id']
        s_filters = self.df_sell['Sell_filter'].unique()
        sell_indexes = list(self.df_sell.index)

        pd_buy = self.df_buy[b_cols].copy()
        pd_buy["Sell_time"] = None
        pd_buy["Sell_filter"] = None
        pd_buy["Sell_profit"] = np.nan
        pd_buy['P_cutloss'] = np.nan
        for s_filter in s_filters:
            pd_buy[f"P_{s_filter}"] = np.nan

        j = 0
        result_buy = []
        for b_index, b_row in pd_buy.iterrows():
            while j < len(sell_indexes) - 1 and sell_indexes[j] <= b_index:
                j += 1
            sell_matching = self.buy_2_sell["all"]
            if b_row['filter'] in self.buy_2_sell.keys():
                sell_matching = self.buy_2_sell[b_row['filter']]

            found_flag = False

            for k in range(j, len(sell_indexes)):
                s_index = sell_indexes[k]
                s_rows = self.df_sell.loc[s_index:s_index]

                # Check T+3
                if s_index - b_index < 3:
                    if s_index == sell_indexes[-1]:  # s_index is hold's index
                        s_row = s_rows.iloc[-1]
                        b_row['P_Hold'] = (s_row['Open_1D'] / b_row['Open_1D'] - 1) * 100
                        b_row['Sell_time'] = s_row['time']
                        b_row['Sell_filter'] = 'Hold'
                        b_row['Sell_profit'] = b_row['P_Hold']
                    continue

                s_index = int(min(s_index, b_row['pre_sell_id']))
                # cutloss
                cutloss_threshold = b_row['Close'] * (1 - self.cutloss)
                if self.df_all.iloc[b_index:s_index + 1]['Close'].min() <= cutloss_threshold:
                    # find cutloss time
                    cutloss_indexes = list(self.df_all.iloc[b_index:s_index + 1][(
                            self.df_all.iloc[b_index:s_index + 1]['Close'] < cutloss_threshold)].index)

                    for c_index in cutloss_indexes:
                        if c_index - b_index >= 3:  # T + 3
                            b_row['P_cutloss'] = (self.df_all.iloc[c_index]['Open_1D'] / b_row['Open_1D'] - 1) * 100
                            b_row['Sell_time'] = self.df_all.iloc[c_index]['time']
                            b_row['Sell_filter'] = 'cutloss'
                            b_row['Sell_profit'] = b_row['P_cutloss']
                            found_flag = True
                            break
                    if found_flag:
                        break

                # Sell by Predefine Sell
                if s_index == b_row['pre_sell_id']:
                    b_row[f"P_{b_row['pre_sell']}"] = b_row['pre_sell_profit'] * 100
                    b_row['Sell_time'] = self.df_all.iloc[s_index]['time']
                    b_row['Sell_filter'] = b_row['pre_sell']
                    b_row['Sell_profit'] = b_row[f"P_{b_row['pre_sell']}"]
                    break

                # Sell by filter
                # move hold to last sell
                if len(s_rows) > 1:
                    if s_rows.iloc[0]['Sell_filter'] == 'Hold':
                        s_rows = s_rows.iloc[1:]._append(s_rows.iloc[0]).reset_index(drop=True)

                for idx in range(len(s_rows)):
                    s_row = s_rows.iloc[idx]
                    if s_row['Sell_filter'] not in sell_matching:
                        continue

                    b_row[f"P_{s_row['Sell_filter']}"] = (s_row['Open_1D'] / b_row['Open_1D'] - 1) * 100
                    b_row["Sell_time"] = s_row['time']
                    b_row['Sell_filter'] = s_row['Sell_filter']
                    b_row['Sell_profit'] = b_row[f"P_{s_row['Sell_filter']}"]
                    found_flag = True

                if found_flag:
                    break

            result_buy.append(b_row)

        result_buy = [data for data in result_buy if not data.empty]
        pd_buy = pd.concat(result_buy, axis=1).T if result_buy else pd_buy

        return pd_buy

    def eval_by_deal(self):
        deal_cols = ['ticker', 'time', 'buy_price', 'sell_price', 'profit', 'sell_filter', 'sell_time', 'filter',
                     'profit_vni']
        l_pd_deal = []
        buy_reasons = list(self.df_buy['filter'].unique())

        for buy_reason in buy_reasons:
            pd_predefine_sell = self.df_buy[self.df_buy['filter'] == buy_reason][
                ['pre_sell', 'pre_sell_profit', 'pre_sell_id']].copy()
            buy_reason_indexes = pd_predefine_sell.index

            # get sell matching from buy_2_sell
            sell_matching = self.buy_2_sell["all"]
            if buy_reason in self.buy_2_sell.keys():
                sell_matching = self.buy_2_sell[buy_reason]

            pd_sell_temp = self.df_sell.query(f"Sell_filter == {sell_matching}").copy()
            sell_indexes = list(pd_sell_temp.index)
            sell_reasons = list(pd_sell_temp['Sell_filter'])

            result = self.find_sell(self.df_all[['time', 'Close', 'Open', 'Open_1D', 'VNINDEX']], pd_predefine_sell,
                                    buy_reason_indexes, sell_indexes, sell_reasons, self.cutloss)

            deal_time = self.df_all.loc[result[0]]['time'].values
            deal_sell_time = self.df_all.loc[result[2]]['time'].values
            pd_deal_pa = pd.DataFrame({
                'time': deal_time,
                'buy_price': result[1],
                'sell_price': result[3],
                'profit': result[4],
                'sell_filter': result[5],
                'sell_time': deal_sell_time,
                'profit_vni': result[6],
            })

            pd_deal_pa.insert(0, 'ticker', self.ticker)
            pd_deal_pa['filter'] = buy_reason

            l_pd_deal.append(pd_deal_pa)
        pd_deal = pd.concat(l_pd_deal, axis=0) if l_pd_deal else pd.DataFrame(columns=deal_cols)

        return pd_deal

    @staticmethod
    def find_sell(df_price: pd.DataFrame, df_predefine_sell: pd.DataFrame, list_buy_index: list, list_sell_index: list,
                  list_sell_reason: list, cutloss):
        """
        Find sell for pair (buy_pattern - sell reason)
        Arguments:
        - df_price: data frame with columns ymd, Close, Open
        - list_buy_index: list of Buy indexes in df_price
        - list_sell_index: list of Sell indexes in df_price
        - list_sell_reason: list of sell reasons
        - cutloss: cutloss threshold from 0 to 1, e.g. 0.15
        Output:
        - list_deal_index: list of first buy indexes for each deal
        - list_deal_buy_price:  list of buying price (open price of T+1)
        - list_deal_sell_index: list of first sell indexes for each deal
        - list_deal_sell_price: list of selling price (open price of T+1)
        - list_deal_profit: list of profit for each group (e.g. 0.20 means 20%)
        - list_deal_result: list of sell reason for each group (cutloss, MA2, MA3, S13, hold)
        """

        def sell_decision(idx):
            price = df_price['Open_1D'].iloc[idx]
            list_deal_sell_index.append(idx)
            list_deal_sell_price.append(price)
            list_deal_profit.append((list_deal_sell_price[-1] / list_deal_buy_price[-1] - 1.0) * 100)
            list_deal_market_price.append(
                (df_price['VNINDEX'].iloc[idx] / df_price['VNINDEX'].iloc[list_deal_index[-1] + 1] - 1.0) * 100)

        # Initialize signal list with "none"
        list_signal = [None] * df_price.shape[0]

        # Mark sell signals with corresponding reasons
        for i, j in zip(list_sell_index, list_sell_reason):
            list_signal[i] = j

        # Mark buy signals
        for i in list_buy_index:
            list_signal[i] = "buy"

        # Initialize output lists
        list_deal_index = []
        list_deal_buy_price = []
        list_deal_sell_index = []
        list_deal_sell_price = []
        list_deal_profit = []
        list_deal_result = []
        list_deal_market_price = []

        current_status = None  # Status can be None or "buy"
        predefine_sell_data = None
        for i in range(min(list_buy_index), df_price.shape[0]):
            # Look for the buy signal
            if current_status is None:
                if list_signal[i] != "buy":
                    continue
                # Do not evaluate deal happens today
                if i < df_price.shape[0] - 1:
                    price = df_price['Open_1D'].iloc[i]
                    list_deal_index.append(i)
                    list_deal_buy_price.append(price)
                    predefine_sell_data = df_predefine_sell.loc[i]
                    current_status = "buy"

            # Look for the cutloss or sell signal
            elif current_status == "buy":

                if i == df_price.shape[0] - 1:  # Reaching the end
                    list_deal_result.append("hold")
                    current_status = None
                    sell_decision(i)
                    continue

                # T + 3
                if i < list_deal_index[-1] + 3:
                    continue

                # Cutloss
                close = df_price['Close'].iloc[i]
                if close < list_deal_buy_price[-1] * (1 - cutloss):
                    list_deal_result.append("cutloss")
                    current_status = None
                    sell_decision(i)
                    continue

                # Sell by predefine sell signal
                if predefine_sell_data['pre_sell_id'] == i:
                    list_deal_result.append(predefine_sell_data['pre_sell'])
                    current_status = None
                    sell_decision(i)
                    continue

                # Sell by pattern signal
                if list_signal[i] != "buy" and list_signal[i] is not None:
                    list_deal_result.append(list_signal[i])
                    current_status = None
                    sell_decision(i)

        return (list_deal_index, list_deal_buy_price, list_deal_sell_index, list_deal_sell_price, list_deal_profit,
                list_deal_result, list_deal_market_price)

    def get_deal(self, **params):
        k_block = params.get('nums_block', 30)
        overheating_block = params.get('enable_block_overheating', False)

        df = self.eval_by_deal()
        df = self._block_buy_signals(df, block_len=k_block, overheating_block=overheating_block)
        extend_cols_buy = ['Volume', 'Volume_1M', 'Volume_1M_P50', 'Open_1D', 'Price', 'Close', 'Open', 'High', 'Low',
                           'Close_T1',
                           'FSCORE', 'D_RSI', 'D_RSI_T1W', 'VAP1W', 'D_MFI', 'D_MFI_T1W', 'D_MACD', 'D_MACD_T1W',
                           'LO_3M_T1', 'D_RSI_Max1W', 'D_RSI_Max3M']
        extend_cols_sell = ['Volume', 'Volume_1M', 'Volume_1M_P50', 'Open_1D', 'Price', 'Close', 'Open', 'High', 'Low',
                            'Close_T1']

        df_extend = self.get_extend_cols(df, extend_cols=extend_cols_buy, on_col=['time'])
        # df_all = self.df_all.copy()
        # df_all['sell_time'] = df_all['time']
        self.df_all['sell_time'] = self.df_all['time']
        df_extend = df_extend.merge(self.df_all[extend_cols_sell + ['sell_time']], on=['sell_time'], how='left',
                                    suffixes=('', '_sell'))

        df_extend.drop_duplicates(subset=['time', 'filter', 'sell_time'], keep='first', inplace=True)

        return df_extend

    def get_hit(self):
        df = self.eval_by_hit()
        return df

    def get_deal_full(self, df_deal):
        cols = ['time']
        for col in self.df_all.columns:
            if col not in df_deal.columns:
                cols.append(col)
        df_deal = df_deal.merge(self.df_all[cols], on=['time'], how='left')
        df_deal.drop_duplicates(subset=['time', 'filter'], keep='first', inplace=True)
        return df_deal


class ShortSellEval(BaseEval):
    def __init__(self, stock, data_frame, dict_filter, cutloss=0.15, cache_service=None):
        super().__init__(stock=stock, data_frame=data_frame, dict_filter=dict_filter, cutloss=cutloss,
                         cache_service=cache_service)

    def eval_short_sell(self):
        deal_cols = ['ticker', 'time', 'buy_price', 'sell_price', 'profit', 'buy_filter', 'buy_time', 'filter',
                     'profit_vni']
        l_pd_deal = []
        sell_reasons = list(self.df_sell['Sell_filter'].unique())

        for sell_reason in sell_reasons:
            sell_reason_indexes = list(self.df_sell[self.df_sell['Sell_filter'] == sell_reason].index)

            buy_indexes = list(self.df_buy.index)
            buy_reasons = list(self.df_buy['filter'])

            result = self.find_short(self.df_all[['time', 'Close', 'Open', 'Open_1D', 'VNINDEX']], sell_reason_indexes,
                                     buy_indexes, buy_reasons, self.cutloss)

            deal_time = self.df_all.loc[result[0]]['time'].values
            deal_buy_time = self.df_all.loc[result[2]]['time'].values
            # (list_deal_index, list_deal_sell_price, list_deal_buy_index, list_deal_buy_price, list_deal_profit,
            #  list_deal_result, list_deal_market_price)
            pd_deal_pa = pd.DataFrame({
                'time': deal_time,
                'sell_price': result[1],
                'buy_price': result[3],
                'profit': result[4],
                'buy_filter': result[5],
                'buy_time': deal_buy_time,
                'profit_vni': result[6],
            })

            pd_deal_pa.insert(0, 'ticker', self.ticker)
            pd_deal_pa['filter'] = sell_reason

            l_pd_deal.append(pd_deal_pa)
        pd_deal = pd.concat(l_pd_deal, axis=0) if l_pd_deal else pd.DataFrame(columns=deal_cols)
        pd_deal = pd_deal.merge(self.df_sell[['time', 'P1W', 'P2W', 'P3W', 'P1M', 'P2M', 'P3M', 'P6M', 'P1Y', 'P2Y']],
                                on='time', how='left')
        pd_deal.drop_duplicates(subset=['time', 'filter'], keep='first', inplace=True)
        return pd_deal

    @staticmethod
    def find_short(df_price: pd.DataFrame, list_sell_index: list, list_buy_index: list, list_buy_reason: list, cutloss):
        """
        Find buy for pair (sell_pattern - buy reason)
        Arguments:
        - df_price: data frame with columns ymd, Close, Open
        - list_sell_index: list of Sell indexes in df_price
        - list_buy_index: list of Buy indexes in df_price
        - list_buy_reason: list of buy reasons
        - cutloss: cutloss threshold from 0 to 1, e.g. 0.15
        Output:
        - list_deal_index: list of first sell indexes for each deal
        - list_deal_sell_price: list of selling price (open price of T+1)
        - list_deal_buy_index: list of first buy indexes for each deal
        - list_deal_buy_price: list of buying price (open price of T+1)
        - list_deal_profit: list of profit for each group (e.g. 0.20 means 20%)
        - list_deal_result: list of buy reason for each group (cutloss, MA2, MA3, S13, hold)
        """

        def buy_decision(idx):
            price = df_price['Open_1D'].iloc[idx]
            list_deal_buy_index.append(idx)
            list_deal_buy_price.append(price)
            list_deal_profit.append((1 - list_deal_buy_price[-1] / list_deal_sell_price[-1]) * 100)
            list_deal_market_price.append(
                (df_price['VNINDEX'].iloc[idx] / df_price['VNINDEX'].iloc[list_deal_index[-1] + 1] - 1.0) * 100)

        skip_period = 22
        limit_period = 20 * 3
        list_signal = [None] * df_price.shape[0]

        # Mark buy signals with corresponding reasons
        for i, j in zip(list_buy_index, list_buy_reason):
            list_signal[i] = j

        # Mark sell signals
        for i in list_sell_index:
            list_signal[i] = "sell"

        # Initialize output lists
        list_deal_index = []
        list_deal_sell_price = []
        list_deal_buy_index = []
        list_deal_buy_price = []
        list_deal_profit = []
        list_deal_result = []
        list_deal_market_price = []

        current_status = None  # Status can be None or "sell"

        for i in range(min(list_sell_index), df_price.shape[0]):

            # Look for the sell signal
            if current_status is None:
                if list_signal[i] != "sell":
                    continue
                # Do not evaluate deal happens today
                if i < df_price.shape[0] - 1:
                    price = df_price['Open_1D'].iloc[i]
                    list_deal_index.append(i)
                    list_deal_sell_price.append(price)
                    current_status = "sell"

            # Look for the cutloss or buy signal
            elif current_status == "sell":

                if i > list_deal_index[-1] + limit_period:  # Reaching the time limit
                    list_deal_result.append("endperiod")
                    current_status = None
                    buy_decision(i)
                    continue

                if i == df_price.shape[0] - 1:  # Reaching the end
                    list_deal_result.append("hold")
                    current_status = None
                    buy_decision(i)
                    continue

                if i < list_deal_index[-1] + 3:  # T+3
                    continue
                # if i < list_deal_index[-1] + skip_period:
                #     continue

                close = df_price['Close'].iloc[i]
                if close > list_deal_sell_price[-1] * (1 + cutloss):  # Cutloss
                    list_deal_result.append("cutloss")
                    current_status = None
                    buy_decision(i)
                    continue

                if list_signal[i] != "sell" and list_signal[i] is not None:  # Buy signal
                    list_deal_result.append(list_signal[i])
                    current_status = None
                    buy_decision(i)

        return (list_deal_index, list_deal_sell_price, list_deal_buy_index, list_deal_buy_price, list_deal_profit,
                list_deal_result, list_deal_market_price)

    def get_shortsell(self):
        df = self.eval_short_sell()

        extend_cols_buy = ['Volume', 'Volume_1M', 'Volume_1M_P50', 'Open_1D', 'Price', 'Close', 'Open', 'High', 'Low',
                           'Close_T1', 'Trading_Session']
        extend_cols_sell = ['Volume', 'Volume_1M', 'Volume_1M_P50', 'Open_1D', 'Price', 'Close', 'Open', 'High', 'Low',
                            'Close_T1', 'Trading_Session']

        df_extend = self.get_extend_cols(df, extend_cols=extend_cols_sell, on_col=['time'])
        # df_all = self.df_all.copy()
        # df_all['sell_time'] = df_all['time']
        self.df_all['buy_time'] = self.df_all['time']
        df_extend = df_extend.merge(self.df_all[extend_cols_buy + ['buy_time']], on=['buy_time'], how='left',
                                    suffixes=('', '_buy'))

        df_extend.drop_duplicates(subset=['time', 'filter', 'buy_time'], keep='first', inplace=True)
        return df_extend


class WeightEval(BaseEval):
    def __init__(self, stock, data_frame, dict_filter, weight, threshold_buy, threshold_sell, cutloss=0.15, lookback=5,
                 k_exp=0, cache_service=None):
        super().__init__(stock=stock, data_frame=data_frame, dict_filter=dict_filter, cutloss=cutloss,
                         cache_service=cache_service)

        self.weight = self.init_weight(weight, dict_filter, threshold_sell, threshold_buy)
        self.threshold_buy = threshold_buy
        self.threshold_sell = threshold_sell
        self.lookback = lookback
        self.exp = k_exp
        self.score_df = self.find_score_base_on_weight()

    @BaseEval.cache_prefix_result(unique=True)
    def sell_signals(self):
        # Apply sell filters
        s_cols = ['ticker', 'time', 'Close', 'Price', 'Open_1D', 'Volume', 'Sell_filter', 'P1W', 'P2W', 'P3W', 'P1M',
                  'P2M', 'P3M', 'P6M', 'P1Y', 'P2Y']

        now = self.df_all.iloc[-1:].copy()
        now['Sell_filter'] = 'Hold'

        sell_data = [now]

        for f in self.dictFilter:
            if f.startswith('~'):
                try:
                    pd_Sell_filtered = self.pd_query(f, self.dictFilter[f])
                    sell_data.append(pd_Sell_filtered)
                except Exception as e:
                    print(f"{self.ticker}--{f[1:]}--error: {e}")

        _sell_data = [data for data in sell_data if not data.empty]
        pd_sell = pd.concat(_sell_data, axis=0).sort_values('time', ascending=True)
        for period in ['1W', '2W', '3W', '1M', '2M', '3M', '6M', '1Y', '2Y']:
            pd_sell[f"P{period}"] = 100. * (pd_sell[f"O{period}"] - 1) * (
                    pd_sell[f"L{period}"] > 1 - self.cutloss) + (-100. * self.cutloss) * (
                                            pd_sell[f"L{period}"] <= 1 - self.cutloss)

        pd_sell = pd_sell[s_cols]
        return pd_sell

    @staticmethod
    def init_weight(w, dict_filter, threshold_sell, threshold_buy):
        if isinstance(w, dict) and w != {}:
            w['Hold'] = threshold_sell
        else:
            w = {'Hold': threshold_sell}
            for k, _ in dict_filter.items():
                if k.startswith('~'):
                    w[k[1:]] = threshold_sell
                if k.startswith('_'):
                    w[k[1:]] = threshold_buy
        return w

    def find_score_base_on_weight(self):
        """
        Input:
        weights: Weight patterns dictionary from hyperopt
        dict_filter: Filter pattern dictionary

        Output:
        si_rerturn

        """
        dAgg = {'deal': 'sum', 'hit': 'sum', 'time': 'last', 'Close': 'last', 'Price': 'last', 'Open_1D': 'last',
                'Volume': 'last', 'score': 'sum', 'n_month': 'sum', 'idx': 'last'}
        d_agg = {}

        df_b = self.get_buy_signals()
        df_s = self.get_sell_signals()
        df = pd.concat([df_b, df_s], axis=0).sort_values('time', ascending=True)
        df['idx'] = df.index

        df['score'] = np.zeros(len(df))
        unique_idx = np.unique(df['idx'].values)  # Lấy các index duy nhất
        idx_arr = df['idx'].values
        filter_arr = df[['filter', 'Sell_filter']].to_numpy()
        score_dict = {}  # Dictionary để cache kết quả

        for now_idx in unique_idx:
            lookback_mask = (idx_arr >= now_idx - self.lookback) & (idx_arr <= now_idx)  # Lọc trong NumPy
            slice_idx = idx_arr[lookback_mask]

            days_ago = now_idx - slice_idx
            decay_factor = np.exp(-self.exp * days_ago)  # Tính decay factor

            slice_filters = filter_arr[lookback_mask].flatten()

            weights = np.zeros_like(slice_filters, dtype=float)
            unique_filters = {}

            for i in range(len(slice_filters) - 1, -1, -1):
                f = slice_filters[i]
                if pd.notna(f) and f not in unique_filters:
                    unique_filters[f] = True
                    weights[i] = self.weight.get(f, 0)

            score = np.sum(weights * decay_factor.repeat(2))
            score_dict[now_idx] = score

        df['score'] = np.vectorize(score_dict.get)(df['idx'])

        for f in list(df.columns):
            if (f not in ['filter', 'ticker', 'time', 'quarter', 'month', 'week', 'sell_time', 'Sell_filter',
                          'sell_filter', 'buy_time', 'buy_filter', 'half_of_year', 'pre_sell',
                          'pre_sell_profit', 'pre_sell_id', 'FILTER']) and ("time" not in f):
                d_agg[f] = dAgg.get(f, 'mean')
        df = df.groupby(['ticker', 'time'], as_index=False).agg(d_agg)
        df = df.set_index('idx')

        # handler for hold
        df['score_status'] = 'score'
        if df.iloc[-1]['score'] >= self.threshold_sell:
            df.at[df.index[-1], 'score'] = self.threshold_sell
            df.at[df.index[-1], 'score_status'] = 'hold'

        return df

        # df_buy = df[df['score'] >= self.threshold_buy].copy()
        # df_sell = df[df['score'] <= self.threshold_sell].copy()
        #
        # return df_buy, df_sell

    def eval_hit_with_weight(self):
        """
        find hits (buy and sell pair
        input:
        pd_buy: all buy signals
        pd_sell: all sell signals
        output:
        pd_hit: all hits
        """

        # pd_buy, pd_sell = self.apply_weight()

        pd_buy = self.score_df[self.score_df['score'] >= self.threshold_buy].copy()
        pd_sell = self.score_df[self.score_df['score'] <= self.threshold_sell].copy()

        sell_indexes = list(pd_sell.index)

        pd_buy["sell_time"] = np.nan
        pd_buy["buy_price"] = np.nan
        pd_buy["sell_filter"] = np.nan
        pd_buy["sell_score"] = np.nan
        pd_buy["sell_price"] = np.nan
        pd_buy["profit"] = np.nan
        pd_buy['p_cutloss'] = np.nan
        # pd_buy['p_hold'] = np.nan
        pd_buy['p_sell'] = np.nan

        j = 0
        result_buy = []
        for b_index, b_row in pd_buy.iterrows():
            while j < len(sell_indexes) - 1 and sell_indexes[j] <= b_index:
                j += 1

            found_flag = False

            for k in range(j, len(sell_indexes)):
                s_index = sell_indexes[k]
                s_rows = pd_sell.loc[s_index:s_index]

                # Check T+3
                if s_index - b_index < 3:
                    if s_index == sell_indexes[-1]:  # s_index is hold's index
                        s_row = s_rows.iloc[-1]
                        b_row['buy_price'] = b_row['Open_1D']
                        b_row['sell_price'] = s_row['Open_1D']
                        b_row['p_sell'] = (s_row['Open_1D'] / b_row['Open_1D'] - 1) * 100
                        b_row['sell_time'] = s_row['time']
                        b_row['sell_filter'] = 'hold'
                        b_row['sell_score'] = -1
                        b_row['profit'] = b_row['p_sell']
                    continue

                # cutloss
                cutloss_threshold = b_row['Close'] * (1 - self.cutloss)
                if self.df_all.iloc[b_index:s_index + 1]['Close'].min() <= cutloss_threshold:
                    # find cutloss time
                    cutloss_indexes = list(self.df_all.iloc[b_index:s_index + 1][(
                            self.df_all.iloc[b_index:s_index + 1]['Close'] < cutloss_threshold)].index)

                    for c_index in cutloss_indexes:
                        if c_index - b_index >= 3:  # T + 3
                            b_row['buy_price'] = b_row['Open_1D']
                            b_row['sell_price'] = self.df_all.iloc[c_index]['Open_1D']
                            b_row['p_cutloss'] = (self.df_all.iloc[c_index]['Open_1D'] / b_row['Open_1D'] - 1) * 100
                            b_row['sell_time'] = self.df_all.iloc[c_index]['time']
                            b_row['sell_filter'] = 'cutloss'
                            b_row['sell_score'] = -1
                            b_row['profit'] = b_row['p_cutloss']
                            found_flag = True
                            break
                    if found_flag:
                        break

                # Sell by filter
                for idx in range(len(s_rows)):
                    s_row = s_rows.iloc[idx]
                    # if s_row['sell_filter'] not in sell_matching:
                    #     continue
                    b_row['buy_price'] = b_row['Open_1D']
                    b_row['sell_price'] = s_row['Open_1D']
                    b_row["p_sell"] = (s_row['Open_1D'] / b_row['Open_1D'] - 1) * 100
                    b_row["sell_time"] = s_row['time']
                    b_row['sell_filter'] = s_row['score_status']
                    b_row['sell_score'] = s_row['score']
                    b_row['profit'] = b_row["p_sell"]
                    found_flag = True

                if found_flag:
                    break

            result_buy.append(b_row)

        result_buy = [data for data in result_buy if not data.empty]
        pd_hit = pd.concat(result_buy, axis=1).T if result_buy else pd_buy

        return pd_hit

    def get_df_weight(self):
        return self.score_df

    def get_eval_weight_hit(self):
        df = self.eval_hit_with_weight()
        extend_cols = ['Volume', 'Volume_1M', 'Volume_1M_P50', 'Open_1D', 'Price', 'Close', 'Open', 'High', 'Low',
                       'Close_T1']
        df_extend = self.get_extend_cols(df, extend_cols=extend_cols)

        self.df_all['sell_time'] = self.df_all['time']
        df_extend = df_extend.merge(self.df_all[extend_cols + ['sell_time']], on=['sell_time'], how='left',
                                    suffixes=('', '_sell'))

        df_extend.drop_duplicates(subset=['time', 'sell_time'], keep='first', inplace=True)

        return df_extend


class TickerEval:
    """
    Evaluate filters for a given ticker
    """

    def __init__(self, stock, data_frame, dict_filter, cutloss=0.15, cache_service=None):
        """
        Initialize the TickerEval class.

        Args:
            stock (str): The stock ticker symbol.
            data_frame (pd.DataFrame): The data frame containing the stock data.
            dict_filter (dict): A dictionary of filters to apply to the data.
            cutloss (float, optional): The cut loss threshold. Defaults to 0.15.
        """
        self.ticker = stock
        self.df_all = data_frame
        self.dictFilter, self.b_f, self.s_f, self.pre_f = self.procress_filter(dict_filter)
        self.cutloss = cutloss
        self.redis_cache = cache_service or get_cache_singleton() or NullCache()

        self.buy_2_sell = self.buy_map_sell()

        self.df_sell = self.sell_signals()
        self.df_buy = self.buy_signals()

    @staticmethod
    def open_df(path, ticker):
        try:
            return pd.read_csv(f'{path}/{ticker}.csv')
        except FileNotFoundError:
            return None

    @staticmethod
    def cache_prefix_result(ticker_name=True, param=False, unique=False, expiry=None):
        """Decorator to cache the result of a method with an optional expiry parameter."""

        def decorator(method):
            @wraps(method)
            def wrapper(self, *args, **kwargs):
                # Generate a unique cache key based on method name, arguments, and keyword arguments
                # key_base = f"{self.__class__.__name__}:{method.__name__}:{args}:{kwargs}"
                method_name = method.__name__
                # print(method_name)
                if method_name == "sell_signals":
                    key_base = f"{json.dumps(self.s_f, sort_keys=True)}"
                elif method_name == 'buy_signals':
                    key_base = f"{json.dumps(self.b_f, sort_keys=True)}:{json.dumps(self.pre_f, sort_keys=True)}"
                # elif method_name == "pd_query":
                #     key_base = f"{args}:{kwargs}"
                #     method_name = f"pd_query:{args[0]}"
                elif param:
                    key_base = f"{args}:{kwargs}"
                    # print(key_base)
                else:
                    key_base = f"{json.dumps(self.dictFilter, sort_keys=True)}"

                if ticker_name:
                    key_base = f"{self.ticker}:{key_base}"

                key_base = f"{self.__class__.__name__}:{key_base}"

                cache_key = self.redis_cache.generate_cache_key(key_base)
                cached_result = self.redis_cache.get_with_expiry_update(prefix=method_name, key=cache_key)
                # Attempt to retrieve from cache
                if cached_result is not None:
                    # print("cache hit")
                    return cached_result

                # Manange unique cache
                if unique:
                    self.redis_cache.manage_prefix_unique_cache(prefix=method_name, r_name=self.ticker, key=cache_key)

                # Call the method and cache its result
                result = method(self, *args, **kwargs)
                self.redis_cache.set_prefix_cache(prefix=method_name, key=cache_key, value=result, expiry=expiry)

                return result

            return wrapper

        return decorator

    @staticmethod
    def procress_filter(dict_filter: dict):
        sell_filter = {}
        buy_filter = {}
        d_filter = {}
        presell_filter = {}
        for k, v in dict_filter.items():
            value = v.strip()
            d_filter[k] = value
            if k.startswith('_'):
                buy_filter[k] = value
            if k.startswith('~'):
                sell_filter[k] = value
            if k.startswith('#'):
                presell_filter = value

        # return {
        #     'full': d_filter,
        #     'b_f':  buy_filter,
        #     's_f': sell_filter,
        #     'pre_f': presell_filter
        #
        # }
        return d_filter, buy_filter, sell_filter, presell_filter

    @cache_prefix_result(param=True, expiry=600)
    def pd_query(self, f, v):
        f_col = "Sell_filter" if f.startswith("~") else "filter"

        cols = ['time', 'ticker', 'Close', 'Price', 'Open_1D', 'Volume', 'C1W', 'C2W', 'C3W', 'C1M', 'C2M', 'C3M',
                'C6M', 'C1Y', 'C2Y', 'L1W', 'L2W', 'L3W', 'L1M', 'L2M', 'L3M', 'L6M', 'L1Y', 'L2Y', 'H1W', 'H2W', 'H3W',
                'H1M', 'H2M', 'H3M', 'H6M', 'H1Y', 'H2Y', 'O1W', 'O2W', 'O3W', 'O1M', 'O2M', 'O3M', 'O6M', 'O1Y',
                'O2Y'] + [f_col]

        try:
            result = self.df_all.query(v).copy()
            result[f_col] = f[1:]
            return result[cols]
        except Exception as e:
            # print(f"[{self.ticker}] pd_query fail `{f}` with query `{v}`: {e}")
            return pd.DataFrame(columns=cols)

    def eval_by_deal(self):
        deal_cols = ['ticker', 'time', 'buy_price', 'sell_price', 'profit', 'sell_filter', 'sell_time', 'filter',
                     'profit_vni']
        l_pd_deal = []
        buy_reasons = list(self.df_buy['filter'].unique())

        for buy_reason in buy_reasons:
            pd_predefine_sell = self.df_buy[self.df_buy['filter'] == buy_reason][
                ['pre_sell', 'pre_sell_profit', 'pre_sell_id']].copy()
            buy_reason_indexes = pd_predefine_sell.index

            # get sell matching from buy_2_sell
            sell_matching = self.buy_2_sell["all"]
            if buy_reason in self.buy_2_sell.keys():
                sell_matching = self.buy_2_sell[buy_reason]

            pd_sell_temp = self.df_sell.query(f"Sell_filter == {sell_matching}").copy()
            sell_indexes = list(pd_sell_temp.index)
            sell_reasons = list(pd_sell_temp['Sell_filter'])

            result = self.find_sell(self.df_all[['time', 'Close', 'Open', 'Open_1D', 'VNINDEX']], pd_predefine_sell,
                                    buy_reason_indexes, sell_indexes, sell_reasons, self.cutloss)

            deal_time = self.df_all.loc[result[0]]['time'].values
            deal_sell_time = self.df_all.loc[result[2]]['time'].values
            pd_deal_pa = pd.DataFrame({
                'time': deal_time,
                'buy_price': result[1],
                'sell_price': result[3],
                'profit': result[4],
                'sell_filter': result[5],
                'sell_time': deal_sell_time,
                'profit_vni': result[6],
            })

            pd_deal_pa.insert(0, 'ticker', self.ticker)
            pd_deal_pa['filter'] = buy_reason

            l_pd_deal.append(pd_deal_pa)
        pd_deal = pd.concat(l_pd_deal, axis=0) if l_pd_deal else pd.DataFrame(columns=deal_cols)

        return pd_deal

    def predefine_sell(self, df_buy):
        df_buy = df_buy.copy()
        df_buy[['pre_sell', 'pre_sell_profit', 'pre_sell_id']] = np.nan
        map_dict = {}
        df = []
        for index, row in df_buy.iterrows():
            result = {}
            # get all pre_pattern and find pre_sell
            b_filter = row['filter']
            if b_filter not in map_dict:
                map_dict[b_filter] = [f for f in self.buy_2_sell.get(b_filter, []) if
                                      f in self.buy_2_sell["pre_pattern"]]
            pre_pattern = map_dict[b_filter]

            # call function
            for pre_sell in pre_pattern:
                command = f"self.{self.buy_2_sell[pre_sell]}".replace(")", f", index={index}, pattern='{pre_sell}')")
                result.update(eval(command))

            # if 'exit_under_profit_expected' in self.buy_2_sell["all"]:
            #     id_min = index + 120
            #     if result:
            #         id_min = min(result.keys())
            #     result.update(self.predefine_exit_under_profit_expected(index, id_min))

            # update dataframe
            if result:
                id = min(result.keys())
                row['pre_sell'] = result[id]['pre_sell']
                row['pre_sell_profit'] = result[id]['pre_sell_profit']
                row['pre_sell_id'] = id

            df.append(row)

        # update df_buy dataframe
        return pd.concat(df, axis=1).T if df else df_buy

    def eval_by_hit(self):
        b_cols = ['filter', 'ticker', 'hit', 'month', 'time', 'Close', 'Price', 'Open_1D', 'Volume', 'P1W', 'P1M',
                  'P3M', 'P6M', 'P1Y', 'P2Y', 'pre_sell', 'pre_sell_profit', 'pre_sell_id']
        s_filters = self.df_sell['Sell_filter'].unique()
        sell_indexes = list(self.df_sell.index)

        pd_buy = self.df_buy[b_cols].copy()
        pd_buy["Sell_time"] = None
        pd_buy["Sell_filter"] = None
        pd_buy["Sell_profit"] = np.nan
        pd_buy['P_cutloss'] = np.nan
        for s_filter in s_filters:
            pd_buy[f"P_{s_filter}"] = np.nan

        j = 0
        result_buy = []
        for b_index, b_row in pd_buy.iterrows():
            while j < len(sell_indexes) - 1 and sell_indexes[j] <= b_index:
                j += 1
            sell_matching = self.buy_2_sell["all"]
            if b_row['filter'] in self.buy_2_sell.keys():
                sell_matching = self.buy_2_sell[b_row['filter']]

            found_flag = False

            for k in range(j, len(sell_indexes)):
                s_index = sell_indexes[k]
                s_rows = self.df_sell.loc[s_index:s_index]

                # Check T+3
                if s_index - b_index < 3:
                    if s_index == sell_indexes[-1]:  # s_index is hold's index
                        s_row = s_rows.iloc[-1]
                        b_row['P_Hold'] = (s_row['Open_1D'] / b_row['Open_1D'] - 1) * 100
                        b_row['Sell_time'] = s_row['time']
                        b_row['Sell_filter'] = 'Hold'
                        b_row['Sell_profit'] = b_row['P_Hold']
                    continue

                s_index = int(min(s_index, b_row['pre_sell_id']))
                # cutloss
                cutloss_threshold = b_row['Close'] * (1 - self.cutloss)
                if self.df_all.iloc[b_index:s_index + 1]['Close'].min() <= cutloss_threshold:
                    # find cutloss time
                    cutloss_indexes = list(self.df_all.iloc[b_index:s_index + 1][(
                            self.df_all.iloc[b_index:s_index + 1]['Close'] < cutloss_threshold)].index)

                    for c_index in cutloss_indexes:
                        if c_index - b_index >= 3:  # T + 3
                            b_row['P_cutloss'] = (self.df_all.iloc[c_index]['Open_1D'] / b_row['Open_1D'] - 1) * 100
                            b_row['Sell_time'] = self.df_all.iloc[c_index]['time']
                            b_row['Sell_filter'] = 'cutloss'
                            b_row['Sell_profit'] = b_row['P_cutloss']
                            found_flag = True
                            break
                    if found_flag:
                        break

                # Sell by Predefine Sell
                if s_index == b_row['pre_sell_id']:
                    b_row[f"P_{b_row['pre_sell']}"] = b_row['pre_sell_profit'] * 100
                    b_row['Sell_time'] = self.df_all.iloc[s_index]['time']
                    b_row['Sell_filter'] = b_row['pre_sell']
                    b_row['Sell_profit'] = b_row[f"P_{b_row['pre_sell']}"]
                    break

                # Sell by filter
                # move hold to last sell
                if len(s_rows) > 1:
                    if s_rows.iloc[0]['Sell_filter'] == 'Hold':
                        s_rows = s_rows.iloc[1:]._append(s_rows.iloc[0]).reset_index(drop=True)

                for idx in range(len(s_rows)):
                    s_row = s_rows.iloc[idx]
                    if s_row['Sell_filter'] not in sell_matching:
                        continue

                    b_row[f"P_{s_row['Sell_filter']}"] = (s_row['Open_1D'] / b_row['Open_1D'] - 1) * 100
                    b_row["Sell_time"] = s_row['time']
                    b_row['Sell_filter'] = s_row['Sell_filter']
                    b_row['Sell_profit'] = b_row[f"P_{s_row['Sell_filter']}"]
                    found_flag = True

                if found_flag:
                    break

            result_buy.append(b_row)

        result_buy = [data for data in result_buy if not data.empty]
        pd_buy = pd.concat(result_buy, axis=1).T if result_buy else pd_buy

        return pd_buy

    def eval_short_sell(self):
        deal_cols = ['ticker', 'time', 'buy_price', 'sell_price', 'profit', 'buy_filter', 'buy_time', 'filter',
                     'profit_vni']
        l_pd_deal = []
        sell_reasons = list(self.df_sell['Sell_filter'].unique())

        for sell_reason in sell_reasons:
            sell_reason_indexes = list(self.df_sell[self.df_sell['Sell_filter'] == sell_reason].index)

            buy_indexes = list(self.df_buy.index)
            buy_reasons = list(self.df_buy['filter'])

            result = self.find_short(self.df_all[['time', 'Close', 'Open', 'Open_1D', 'VNINDEX']], sell_reason_indexes,
                                     buy_indexes, buy_reasons, self.cutloss)

            deal_time = self.df_all.loc[result[0]]['time'].values
            deal_buy_time = self.df_all.loc[result[2]]['time'].values
            # (list_deal_index, list_deal_sell_price, list_deal_buy_index, list_deal_buy_price, list_deal_profit,
            #  list_deal_result, list_deal_market_price)
            pd_deal_pa = pd.DataFrame({
                'time': deal_time,
                'sell_price': result[1],
                'buy_price': result[3],
                'profit': result[4],
                'buy_filter': result[5],
                'buy_time': deal_buy_time,
                'profit_vni': result[6],
            })

            pd_deal_pa.insert(0, 'ticker', self.ticker)
            pd_deal_pa['filter'] = sell_reason

            l_pd_deal.append(pd_deal_pa)
        pd_deal = pd.concat(l_pd_deal, axis=0) if l_pd_deal else pd.DataFrame(columns=deal_cols)
        pd_deal = pd_deal.merge(self.df_sell[['time', 'P1W', 'P2W', 'P3W', 'P1M', 'P2M', 'P3M', 'P6M', 'P1Y', 'P2Y']],
                                on='time', how='left')
        pd_deal.drop_duplicates(subset=['time', 'filter'], keep='first', inplace=True)
        return pd_deal

    @cache_prefix_result(unique=True)
    def sell_signals(self):
        # Apply sell filters
        s_cols = ['ticker', 'time', 'Close', 'Price', 'Open_1D', 'Volume', 'Sell_filter', 'P1W', 'P2W', 'P3W', 'P1M',
                  'P2M', 'P3M', 'P6M', 'P1Y', 'P2Y']

        now = self.df_all.iloc[-1:].copy()
        now['Sell_filter'] = 'Hold'

        sell_data = [now]

        for f in self.dictFilter:
            if f.startswith('~'):
                try:
                    pd_Sell_filtered = self.pd_query(f, self.dictFilter[f])
                    sell_data.append(pd_Sell_filtered)
                except Exception as e:
                    # print(f"{self.ticker}--{f[1:]}--error: {e}")
                    pass

        _sell_data = [data for data in sell_data if not data.empty]

        # for profit for shortsell
        pd_sell = pd.concat(_sell_data, axis=0).sort_values('time', ascending=True)
        for period in ['1W', '2W', '3W', '1M', '2M', '3M', '6M', '1Y', '2Y']:
            pd_sell[f"P{period}"] = 100. * (1 - pd_sell[f"O{period}"]) * (
                    pd_sell[f"H{period}"] < 1 + self.cutloss) + (-100. * self.cutloss) * (
                                            pd_sell[f"H{period}"] >= 1 + self.cutloss)

        pd_sell = pd_sell[s_cols]
        return pd_sell

    @cache_prefix_result(unique=True)
    def buy_signals(self):
        b_cols = ['time', 'ticker', 'Close', 'Price', 'Open_1D', 'Volume', 'O1W', 'O2W', 'O3W', 'O1M', 'O2M', 'O3M',
                  'O6M', 'O1Y', 'O2Y', 'P1W', 'P2W', 'P3W', 'P1M', 'P2M', 'P3M', 'P6M', 'P1Y', 'P2Y', 'filter', 'hit',
                  'month']

        # Apply buy filters
        buy_data = []

        for f in self.dictFilter:
            if f.startswith('_'):
                try:
                    pd_buy_filtered = self.pd_query(f, self.dictFilter[f])
                    buy_data.append(pd_buy_filtered)
                except Exception as e:
                    # print(f"{self.ticker}--{f[1:]}--error: {e}")
                    pass

        _buy_data = [data for data in buy_data if not data.empty]
        if _buy_data:
            pd_buy = pd.concat(_buy_data, axis=0).sort_values('time', ascending=True)

            for period in ['1W', '2W', '3W', '1M', '2M', '3M', '6M', '1Y', '2Y']:
                pd_buy[f"P{period}"] = 100. * (pd_buy[f"O{period}"] - 1) * (
                        pd_buy[f"L{period}"] > 1 - self.cutloss) + (-100. * self.cutloss) * (
                                               pd_buy[f"L{period}"] <= 1 - self.cutloss)
        else:
            pd_buy = pd.DataFrame(columns=b_cols)

        pd_buy['hit'] = 1
        pd_buy['month'] = pd_buy['time'].map(lambda x: x[:7])

        return self.predefine_sell(pd_buy[b_cols])

    @cache_prefix_result(ticker_name=False, expiry=300)
    def buy_map_sell(self):
        all_filters = ["Hold"]
        pre_pattern = []
        buy2sell = {}

        for f in self.dictFilter:
            if f.startswith("$"):
                sell_key = f[1:]
                value = [x.replace("~", "").strip() for x in self.dictFilter[f].split(",")]
                value.append("Hold")
                buy2sell[sell_key] = value
            if f.startswith('#'):
                sell_key = f[1:]
                buy2sell[sell_key] = self.dictFilter[f]
                pre_pattern.append(sell_key)

        buy2sell['pre_pattern'] = pre_pattern

        for f in self.dictFilter:
            if f.startswith('~'):
                all_filters.append(f[1:])
            if f.endswith('~'):
                for k, v in buy2sell.items():
                    if f[1:] not in v:
                        buy2sell[k].append(f[1:])

        buy2sell['all'] = list(set(all_filters))

        return buy2sell

    def get_buy_signals(self):
        # cols = ['time', 'ticker', 'Close', 'Open_1D', 'Volume', 'filter', 'pre_sell', 'pre_sell_profit', 'pre_sell_id']
        cols = ['time', 'ticker', 'Close', 'Price', 'Open_1D', 'Volume', 'filter', 'P1W', 'P2W', 'P3W', 'P1M', 'P2M',
                'P3M', 'P6M', 'P1Y', 'P2Y']
        return self.df_buy[cols]

    def get_sell_signals(self):
        cols = ['time', 'ticker', 'Close', 'Price', 'Open_1D', 'Volume', 'Sell_filter', 'P1W', 'P2W', 'P3W', 'P1M',
                'P2M', 'P3M', 'P6M', 'P1Y', 'P2Y']
        return self.df_sell[cols]

    def get_extend_cols(self, df_deal, extend_cols, on_col=None):
        if on_col is None:
            on_col = ['time']
        cols = on_col.copy()
        for col in extend_cols:
            if (col not in df_deal.columns) and (col in self.df_all.columns):
                cols.append(col)
        df_deal = df_deal.merge(self.df_all[cols], on=on_col, how='left')
        return df_deal

    def get_deal(self):
        df = self.eval_by_deal()
        extend_cols_buy = ['Volume', 'Volume_1M', 'Volume_1M_P50', 'Open_1D', 'Price', 'Close', 'Open', 'High', 'Low',
                           'Close_T1',
                           'FSCORE', 'D_RSI', 'D_RSI_T1W', 'VAP1W', 'D_MFI', 'D_MFI_T1W', 'D_MACD', 'D_MACD_T1W',
                           'LO_3M_T1', 'D_RSI_Max1W', 'D_RSI_Max3M']
        extend_cols_sell = ['Volume', 'Volume_1M', 'Volume_1M_P50', 'Open_1D', 'Price', 'Close', 'Open', 'High', 'Low',
                            'Close_T1']

        df_extend = self.get_extend_cols(df, extend_cols=extend_cols_buy, on_col=['time'])
        # df_all = self.df_all.copy()
        # df_all['sell_time'] = df_all['time']
        self.df_all['sell_time'] = self.df_all['time']
        df_extend = df_extend.merge(self.df_all[extend_cols_sell + ['sell_time']], on=['sell_time'], how='left',
                                    suffixes=('', '_sell'))

        df_extend.drop_duplicates(subset=['time', 'filter', 'sell_time'], keep='first', inplace=True)

        return df_extend

    def get_hit(self):
        df = self.eval_by_hit()
        return df

    def append_full_indicators(self, df_00):
        cols = ['time']
        for col in self.df_all.columns:
            if col not in df_00.columns:
                cols.append(col)
        df_deal = df_00.merge(self.df_all[cols], on=['time'], how='left')
        df_deal.drop_duplicates(subset=['time', 'filter'], keep='first', inplace=True)
        return df_deal

    @staticmethod
    def find_sell(df_price: pd.DataFrame, df_predefine_sell: pd.DataFrame, list_buy_index: list, list_sell_index: list,
                  list_sell_reason: list, cutloss):
        """
        Find sell for pair (buy_pattern - sell reason)
        Arguments:
        - df_price: data frame with columns ymd, Close, Open
        - list_buy_index: list of Buy indexes in df_price
        - list_sell_index: list of Sell indexes in df_price
        - list_sell_reason: list of sell reasons
        - cutloss: cutloss threshold from 0 to 1, e.g. 0.15
        Output:
        - list_deal_index: list of first buy indexes for each deal
        - list_deal_buy_price:  list of buying price (open price of T+1)
        - list_deal_sell_index: list of first sell indexes for each deal
        - list_deal_sell_price: list of selling price (open price of T+1)
        - list_deal_profit: list of profit for each group (e.g. 0.20 means 20%)
        - list_deal_result: list of sell reason for each group (cutloss, MA2, MA3, S13, hold)
        """

        def sell_decision(idx):
            price = df_price['Open_1D'].iloc[idx]
            list_deal_sell_index.append(idx)
            list_deal_sell_price.append(price)
            list_deal_profit.append((list_deal_sell_price[-1] / list_deal_buy_price[-1] - 1.0) * 100)
            list_deal_market_price.append(
                (df_price['VNINDEX'].iloc[idx] / df_price['VNINDEX'].iloc[list_deal_index[-1] + 1] - 1.0) * 100)

        # Initialize signal list with "none"
        list_signal = [None] * df_price.shape[0]

        # Mark sell signals with corresponding reasons
        for i, j in zip(list_sell_index, list_sell_reason):
            list_signal[i] = j

        # Mark buy signals
        for i in list_buy_index:
            list_signal[i] = "buy"

        # Initialize output lists
        list_deal_index = []
        list_deal_buy_price = []
        list_deal_sell_index = []
        list_deal_sell_price = []
        list_deal_profit = []
        list_deal_result = []
        list_deal_market_price = []

        current_status = None  # Status can be None or "buy"
        predefine_sell_data = None
        for i in range(min(list_buy_index), df_price.shape[0]):
            # Look for the buy signal
            if current_status is None:
                if list_signal[i] != "buy":
                    continue
                # Do not evaluate deal happens today
                if i < df_price.shape[0] - 1:
                    price = df_price['Open_1D'].iloc[i]
                    list_deal_index.append(i)
                    list_deal_buy_price.append(price)
                    predefine_sell_data = df_predefine_sell.loc[i]
                    current_status = "buy"

            # Look for the cutloss or sell signal
            elif current_status == "buy":

                if i == df_price.shape[0] - 1:  # Reaching the end
                    list_deal_result.append("hold")
                    current_status = None
                    sell_decision(i)
                    continue

                # T + 3
                if i < list_deal_index[-1] + 3:
                    continue

                # Cutloss
                close = df_price['Close'].iloc[i]
                if close < list_deal_buy_price[-1] * (1 - cutloss):
                    list_deal_result.append("cutloss")
                    current_status = None
                    sell_decision(i)
                    continue

                # Sell by predefine sell signal
                if predefine_sell_data['pre_sell_id'] == i:
                    list_deal_result.append(predefine_sell_data['pre_sell'])
                    current_status = None
                    sell_decision(i)
                    continue

                # Sell by pattern signal
                if list_signal[i] != "buy" and list_signal[i] is not None:
                    list_deal_result.append(list_signal[i])
                    current_status = None
                    sell_decision(i)

        return (list_deal_index, list_deal_buy_price, list_deal_sell_index, list_deal_sell_price, list_deal_profit,
                list_deal_result, list_deal_market_price)

    @staticmethod
    def find_short(df_price: pd.DataFrame, list_sell_index: list, list_buy_index: list, list_buy_reason: list, cutloss):
        """
        Find buy for pair (sell_pattern - buy reason)
        Arguments:
        - df_price: data frame with columns ymd, Close, Open
        - list_sell_index: list of Sell indexes in df_price
        - list_buy_index: list of Buy indexes in df_price
        - list_buy_reason: list of buy reasons
        - cutloss: cutloss threshold from 0 to 1, e.g. 0.15
        Output:
        - list_deal_index: list of first sell indexes for each deal
        - list_deal_sell_price: list of selling price (open price of T+1)
        - list_deal_buy_index: list of first buy indexes for each deal
        - list_deal_buy_price: list of buying price (open price of T+1)
        - list_deal_profit: list of profit for each group (e.g. 0.20 means 20%)
        - list_deal_result: list of buy reason for each group (cutloss, MA2, MA3, S13, hold)
        """

        def buy_decision(idx):
            price = df_price['Open_1D'].iloc[idx]
            list_deal_buy_index.append(idx)
            list_deal_buy_price.append(price)
            list_deal_profit.append((1 - list_deal_buy_price[-1] / list_deal_sell_price[-1]) * 100)
            list_deal_market_price.append(
                (df_price['VNINDEX'].iloc[idx] / df_price['VNINDEX'].iloc[list_deal_index[-1] + 1] - 1.0) * 100)

        skip_period = 22
        limit_period = 20 * 3
        list_signal = [None] * df_price.shape[0]

        # Mark buy signals with corresponding reasons
        for i, j in zip(list_buy_index, list_buy_reason):
            list_signal[i] = j

        # Mark sell signals
        for i in list_sell_index:
            list_signal[i] = "sell"

        # Initialize output lists
        list_deal_index = []
        list_deal_sell_price = []
        list_deal_buy_index = []
        list_deal_buy_price = []
        list_deal_profit = []
        list_deal_result = []
        list_deal_market_price = []

        current_status = None  # Status can be None or "sell"

        for i in range(min(list_sell_index), df_price.shape[0]):

            # Look for the sell signal
            if current_status is None:
                if list_signal[i] != "sell":
                    continue
                # Do not evaluate deal happens today
                if i < df_price.shape[0] - 1:
                    price = df_price['Open_1D'].iloc[i]
                    list_deal_index.append(i)
                    list_deal_sell_price.append(price)
                    current_status = "sell"

            # Look for the cutloss or buy signal
            elif current_status == "sell":

                if i > list_deal_index[-1] + limit_period:  # Reaching the time limit
                    list_deal_result.append("endperiod")
                    current_status = None
                    buy_decision(i)
                    continue

                if i == df_price.shape[0] - 1:  # Reaching the end
                    list_deal_result.append("hold")
                    current_status = None
                    buy_decision(i)
                    continue

                if i < list_deal_index[-1] + 3:  # T+3
                    continue
                # if i < list_deal_index[-1] + skip_period:
                #     continue

                close = df_price['Close'].iloc[i]
                if close > list_deal_sell_price[-1] * (1 + cutloss):  # Cutloss
                    list_deal_result.append("cutloss")
                    current_status = None
                    buy_decision(i)
                    continue

                if list_signal[i] != "sell" and list_signal[i] is not None:  # Buy signal
                    list_deal_result.append(list_signal[i])
                    current_status = None
                    buy_decision(i)

        return (list_deal_index, list_deal_sell_price, list_deal_buy_index, list_deal_buy_price, list_deal_profit,
                list_deal_result, list_deal_market_price)

    def exit_under_profit(self, period, value, index, pattern):
        """
        Using for predefine exit
        """
        DICT_SHIFT = {'1W': 5, '2W': 10, '3W': 15, '1M': 20, '2M': 40, '3M': 60, '6M': 120, '1Y': 240, '2Y': 480}

        res = {}
        if (self.df_buy.loc[index, f'O{period}'] - 1 < value).all():
            key = min(index + DICT_SHIFT[period], self.df_all.shape[0] - 1)
            res[key] = {"pre_sell": pattern,
                        "pre_sell_profit": np.mean(self.df_buy.loc[index, f'O{period}'] - 1)}
        return res

    def predefine_exit_under_profit_expected(self, index, id_min=1000000):
        w1 = 0.994
        w2 = 0.072
        w3 = -0.341
        res = {}

        duratrion = min(self.df_all.shape[0] - 1 - index, id_min - index)
        start = index + 3
        end = start + duratrion

        df_data = self.df_all[start:end].copy()
        df_data['holding_session'] = list(range(3, 3 + len(df_data)))
        # df_data['label_p1m'] = df_data['Close_1M'] / df_data['Close_y']

        df_data['pred'] = w1 * (df_data['Close'] / df_data['Close_T1M']) + w2 * (df_data['holding_session'] / 30) + w3
        key = df_data[df_data['pred'] < 1.03].index
        if list(key):
            key = min(df_data[df_data['pred'] < 1.03].index)
            res[key] = {"pre_sell": "exit_under_profit_expected",
                        "pre_sell_profit": self.df_all.loc[key, 'Open_1D'] / np.mean(
                            self.df_buy.loc[index, 'Open_1D'] - 1)}

        return res

    # Overlap
    def find_buy_overlap(self):
        """
        Finds the overlap of buy signals in the dataset.

        This method processes buy and sell signals, counts the occurrences of each filter,
        and calculates the total overlap of buy signals.

        Returns:
            dict: A dictionary containing the count of overlaps for each filter and the total count.
        """
        pd_sell = self.get_sell_signals()
        pd_sell = pd_sell['time'].copy()
        pd_sell['status'] = 'sell'

        pd_buy = self.get_buy_signals()
        pd_buy = pd_buy[['time', 'filter']].copy()
        pd_buy['status'] = 'buy'

        df = pd.concat([pd_buy, pd_sell], axis=0).sort_values('time', ascending=True)

        result = {'total': 0}
        for f in self.b_f.keys():
            result[f[1:]] = 0
            result[f'total_{f[1:]}'] = 0

        count = []
        for _, data in df.iterrows():
            if data['status'] == 'sell':
                count = [c for c in count if not pd.isna(c)]
                if len(count) == 1:
                    result[count[0]] += 1
                    result[f'total_{count[0]}'] += 1
                    result['total'] += 1

                elif len(count) > 1:
                    for f in count:
                        # result[f'total_{f}'] += len(count)
                        result[f'total_{f}'] += 1
                    result['total'] += 1

                count = []
            elif data['status'] == 'buy':
                if data['filter'] not in count:
                    count.append(data['filter'])

        return result

    def find_sell_overlap(self):
        """
        Finds the overlap of sell signals in the dataset.

        This method processes sell signals, counts the occurrences of each filter, if the sell signal is appear in
        same week and calculates the total overlap of sell signals

        Returns:
            dict: A dictionary containing the count of overlaps for each filter and the total count.
        """

        pd_sell = self.get_sell_signals()
        pd_sell = pd_sell[['time', 'Sell_filter']].copy()
        pd_sell['status'] = 'sell'

        # pd_sell['week'] = pd.to_datetime(pd_sell['time']).dt.to_period('W').dt.strftime('%Y-%U')
        # pd_sell['month'] = pd_sell['time'].str[:7]

        result = {'total': 0}
        for f in self.s_f.keys():
            result[f[1:]] = 0
            result[f'total_{f[1:]}'] = 0

        # group and count each sell signal in same week
        for date, group in pd_sell.groupby('time'):
            filters = [f for f in group['Sell_filter'].unique() if f != 'Hold']

            if len(filters) == 1:
                result[filters[0]] += 1
                result[f'total_{filters[0]}'] += 1
                result['total'] += 1
            elif len(filters) > 1:
                for f in filters:
                    result[f'total_{f}'] += 1
                    result['total'] += 1

        return result

    def find_buy_overlap_pair(self):
        """
        Finds the overlap of buy signals in the dataset.

        This method processes buy and sell signals, counts the occurrences of each filter,
        and calculates the total overlap of buy signals.

        Returns:
            dict: A dictionary containing the count of overlaps for each filter and the total count.
        """
        pd_sell = self.get_sell_signals()
        pd_sell = pd_sell['time'].copy()
        pd_sell['status'] = 'sell'

        pd_buy = self.get_buy_signals()
        pd_buy = pd_buy[['time', 'filter']].copy()
        pd_buy['status'] = 'buy'
        # df = pd.concat([pd_buy, pd_sell], axis=0).sort_values('time', ascending=True)

        # a = [list(comb) for comb in combinations(list(self.b_f.keys()), 2)]

        filters = [f[1:] for f in self.b_f.keys()]
        results = {}
        for i in range(len(filters)):
            for j in range(len(filters)):
                result = {'total': 0}
                fi = filters[i]
                fj = filters[j]

                result[fi] = 0
                result[fj] = 0
                result[f'total_{fi}'] = 0
                result[f'total_{fj}'] = 0

                count = []
                pd_buy_slice = pd_buy[pd_buy['filter'].isin([filters[i], filters[j]])]
                df = pd.concat([pd_buy_slice, pd_sell], axis=0).sort_values('time', ascending=True)

                for _, data in df.iterrows():
                    if data['status'] == 'sell':
                        count = [c for c in count if not pd.isna(c)]
                        if len(count) == 1:
                            result[count[0]] += 1
                            result[f'total_{count[0]}'] += 1
                            result['total'] += 1

                        elif len(count) > 1:
                            for f in count:
                                # result[f'total_{f}'] += len(count)
                                result[f'total_{f}'] += 1
                            result['total'] += 1

                        count = []
                    elif data['status'] == 'buy':
                        if data['filter'] not in count:
                            count.append(data['filter'])

                results[f'{fi}_to_{fj}'] = result[fi]
                results[f'total_{fi}_to_{fj}'] = result[f'total_{fi}']
                results[f'{fj}_to_{fi}'] = result[fj]
                results[f'total_{fj}_to_{fi}'] = result[f'total_{fj}']
        return results

    def find_overlap(self):
        res_s = self.find_sell_overlap()
        res_b = self.find_buy_overlap()
        res = dict()
        res.update(res_b)
        res.update(res_s)
        return res


class FilterOverlap(BaseEval):
    def __init__(self):
        super().__init__()

    # Overlap
    def find_buy_overlap(self):
        """
        Finds the overlap of buy signals in the dataset.

        This method processes buy and sell signals, counts the occurrences of each filter,
        and calculates the total overlap of buy signals.

        Returns:
            dict: A dictionary containing the count of overlaps for each filter and the total count.
        """
        pd_sell = self.get_sell_signals()
        pd_sell = pd_sell['time'].copy()
        pd_sell['status'] = 'sell'

        pd_buy = self.get_buy_signals()
        pd_buy = pd_buy[['time', 'filter']].copy()
        pd_buy['status'] = 'buy'

        df = pd.concat([pd_buy, pd_sell], axis=0).sort_values('time', ascending=True)

        result = {'total': 0}
        for f in self.b_f.keys():
            result[f[1:]] = 0
            result[f'total_{f[1:]}'] = 0

        count = []
        for _, data in df.iterrows():
            if data['status'] == 'sell':
                count = [c for c in count if not pd.isna(c)]
                if len(count) == 1:
                    result[count[0]] += 1
                    result[f'total_{count[0]}'] += 1
                    result['total'] += 1

                elif len(count) > 1:
                    for f in count:
                        # result[f'total_{f}'] += len(count)
                        result[f'total_{f}'] += 1
                    result['total'] += 1

                count = []
            elif data['status'] == 'buy':
                if data['filter'] not in count:
                    count.append(data['filter'])

        return result

    def find_sell_overlap(self):
        """
        Finds the overlap of sell signals in the dataset.

        This method processes sell signals, counts the occurrences of each filter, if the sell signal is appear in
        same week and calculates the total overlap of sell signals

        Returns:
            dict: A dictionary containing the count of overlaps for each filter and the total count.
        """

        pd_sell = self.get_sell_signals()
        pd_sell = pd_sell[['time', 'Sell_filter']].copy()
        pd_sell['status'] = 'sell'

        # pd_sell['week'] = pd.to_datetime(pd_sell['time']).dt.to_period('W').dt.strftime('%Y-%U')
        # pd_sell['month'] = pd_sell['time'].str[:7]

        result = {'total': 0}
        for f in self.s_f.keys():
            result[f[1:]] = 0
            result[f'total_{f[1:]}'] = 0

        # group and count each sell signal in same week
        for date, group in pd_sell.groupby('time'):
            filters = [f for f in group['Sell_filter'].unique() if f != 'Hold']

            if len(filters) == 1:
                result[filters[0]] += 1
                result[f'total_{filters[0]}'] += 1
                result['total'] += 1
            elif len(filters) > 1:
                for f in filters:
                    result[f'total_{f}'] += 1
                    result['total'] += 1

        return result

    def find_buy_overlap_pair(self):
        """
        Finds the overlap of buy signals in the dataset.

        This method processes buy and sell signals, counts the occurrences of each filter,
        and calculates the total overlap of buy signals.

        Returns:
            dict: A dictionary containing the count of overlaps for each filter and the total count.
        """
        pd_sell = self.get_sell_signals()
        pd_sell = pd_sell['time'].copy()
        pd_sell['status'] = 'sell'

        pd_buy = self.get_buy_signals()
        pd_buy = pd_buy[['time', 'filter']].copy()
        pd_buy['status'] = 'buy'
        # df = pd.concat([pd_buy, pd_sell], axis=0).sort_values('time', ascending=True)

        # a = [list(comb) for comb in combinations(list(self.b_f.keys()), 2)]

        filters = [f[1:] for f in self.b_f.keys()]
        results = {}
        for i in range(len(filters)):
            for j in range(len(filters)):
                result = {'total': 0}
                fi = filters[i]
                fj = filters[j]

                result[fi] = 0
                result[fj] = 0
                result[f'total_{fi}'] = 0
                result[f'total_{fj}'] = 0

                count = []
                pd_buy_slice = pd_buy[pd_buy['filter'].isin([filters[i], filters[j]])]
                df = pd.concat([pd_buy_slice, pd_sell], axis=0).sort_values('time', ascending=True)

                for _, data in df.iterrows():
                    if data['status'] == 'sell':
                        count = [c for c in count if not pd.isna(c)]
                        if len(count) == 1:
                            result[count[0]] += 1
                            result[f'total_{count[0]}'] += 1
                            result['total'] += 1

                        elif len(count) > 1:
                            for f in count:
                                # result[f'total_{f}'] += len(count)
                                result[f'total_{f}'] += 1
                            result['total'] += 1

                        count = []
                    elif data['status'] == 'buy':
                        if data['filter'] not in count:
                            count.append(data['filter'])

                results[f'{fi}_to_{fj}'] = result[fi]
                results[f'total_{fi}_to_{fj}'] = result[f'total_{fi}']
                results[f'{fj}_to_{fi}'] = result[fj]
                results[f'total_{fj}_to_{fi}'] = result[f'total_{fj}']
        return results

    def find_overlap(self):
        res_s = self.find_sell_overlap()
        res_b = self.find_buy_overlap()
        res = dict()
        res.update(res_b)
        res.update(res_s)
        return res


class PreProcess:
    def __init__(self):
        self.dAgg = {'deal': 'sum', 'hit': 'sum', 'time': 'last', 'Close': 'last', 'count_cutloss': 'count',
                     'n_month': 'sum',
                     'count_hold': 'count', 'count_win': 'count', 'count_loss': 'count', 'count_sell': 'count',
                     'count_hold_win': 'count', 'count_hold_loss': 'count', 'n_quarter': 'sum', 'sum_profit': 'sum',
                     }

    def group_by(self, df: pd.DataFrame, by: list, d_agg: dict = None):
        if d_agg is None:
            d_agg = {}
        for f in list(df.columns):
            if f.startswith("spec_col_count"):
                d_agg[f] = 'count'
            elif f.startswith("spec_col_sum"):
                d_agg[f] = 'sum'
            elif (f not in ['filter', 'ticker', 'time', 'quarter', 'quarter_fr', 'month', 'week', 'Close', 'sell_time',
                            'sell_filter', 'Sell_filter', 'buy_time', 'buy_filter', 'half_of_year', 'pre_sell',
                            'pre_sell_profit', 'pre_sell_id', 'score_status']) and ("time" not in f):
                if f not in d_agg:
                    d_agg[f] = self.dAgg.get(f, 'mean')

        df = df.groupby(by, as_index=False).agg(d_agg)
        return df

    def deals(self, pd_deal):
        pd_deal[['p_hold', 'p_cutloss']] = np.nan
        for idx, result in enumerate(pd_deal['sell_filter'].values):
            if f'p_{result}' not in pd_deal.columns:
                pd_deal[f'p_{result}'] = np.nan
            pd_deal.loc[idx, f'p_{result}'] = pd_deal.loc[idx, 'profit']

        p_sell_columns = [col for col in pd_deal.columns if
                          (col.startswith('p_') and col not in ['p_cutloss', 'p_hold'])]
        pd_deal["p_sell_pattern"] = pd.concat([pd_deal[col].dropna() for col in p_sell_columns])

        pd_deal['p_win'] = pd_deal['p_sell_pattern'].where(pd_deal['p_sell_pattern'] > 0, np.nan)
        pd_deal['p_loss'] = pd_deal['p_sell_pattern'].where(pd_deal['p_sell_pattern'] <= 0, np.nan)

        pd_deal['count_win'] = pd_deal['p_win'].copy()
        pd_deal['count_loss'] = pd_deal['p_loss'].copy()
        pd_deal['count_hold'] = pd_deal['p_hold'].copy()
        pd_deal['count_hold_win'] = pd_deal['p_hold'].where(pd_deal['p_hold'] > 0, np.nan)
        pd_deal['count_hold_loss'] = pd_deal['p_hold'].where(pd_deal['p_hold'] <= 0, np.nan)
        pd_deal['count_cutloss'] = pd_deal['p_cutloss'].copy()

        pd_deal["quarter"] = pd.to_datetime(pd_deal['time']).dt.to_period('Q').astype(str)
        pd_deal["quarter_fr"] = pd_deal['time'].apply(lambda x: self.quarter_report(x))

        pd_deal['holding_period'] = (pd.to_datetime(pd_deal['sell_time']) - pd.to_datetime(pd_deal['time'])).dt.days
        pd_deal['half_of_year'] = pd_deal['time'].str[:4] + "H" + pd.to_datetime(pd_deal['time']).dt.month.gt(6).add(
            1).astype(str)

        pd_deal['deal'] = 1
        for ticker in pd_deal['ticker'].unique():
            ticker_data = pd_deal[pd_deal['ticker'] == ticker]
            if len(ticker_data) > 1:  # Check if there's more than one data point
                pd_deal.loc[pd_deal['ticker'] == ticker, 'corr'] = ticker_data['profit'].corr(ticker_data['profit_vni'])
            else:
                pd_deal.loc[pd_deal['ticker'] == ticker, 'corr'] = np.nan
        for b_pattern in pd_deal['filter'].unique():
            pd_deal.loc[pd_deal['filter'] == b_pattern, 'entropy'] = self.calculate_entropy(
                pd_deal[pd_deal['filter'] == b_pattern]['half_of_year'])
            pd_deal.loc[pd_deal['filter'] == b_pattern, 'n_half'] = \
                pd_deal[pd_deal['filter'] == b_pattern]['half_of_year'].unique().shape[0]

        return pd_deal

    def hits(self, pd_b):
        pd_b['week'] = self.convert_to_yyyy_w(pd_b['time'])
        for b_pattern in pd_b['Sell_filter'].unique():
            if b_pattern not in ['Hold', 'cutloss']:
                pd_b[f'spec_col_count_{b_pattern}'] = pd_b[f'P_{b_pattern}'].copy()

        pd_b['P_Sell'] = pd_b[(pd_b['Sell_filter'] != 'Hold') & (pd_b['Sell_filter'] != 'cutloss')]['Sell_profit']
        pd_b['count_win'] = pd_b['P_Sell'].where(pd_b['P_Sell'] > 0, np.nan)
        pd_b['count_loss'] = pd_b['P_Sell'].where(pd_b['P_Sell'] <= 0, np.nan)
        pd_b['count_hold'] = pd_b['P_Hold'].copy()
        pd_b['count_cutloss'] = pd_b['P_cutloss'].copy()
        pd_b['holding_period'] = (pd.to_datetime(pd_b['Sell_time']) - pd.to_datetime(pd_b['time'])).dt.days

        return pd_b

    def shortsell(self, pd_short):
        pd_short[['p_hold', 'p_cutloss']] = np.nan
        for idx, result in enumerate(pd_short['buy_filter'].values):
            if f'p_{result}' not in pd_short.columns:
                pd_short[f'p_{result}'] = np.nan
            pd_short.loc[idx, f'p_{result}'] = pd_short.loc[idx, 'profit']

        p_buy_columns = [col for col in pd_short.columns if
                         (col.startswith('p_') and col not in ['p_cutloss', 'p_hold'])]
        pd_short["p_buy_pattern"] = pd.concat([pd_short[col].dropna() for col in p_buy_columns])

        pd_short['p_win'] = pd_short['p_buy_pattern'].where(pd_short['p_buy_pattern'] > 0, np.nan)
        pd_short['p_loss'] = pd_short['p_buy_pattern'].where(pd_short['p_buy_pattern'] <= 0, np.nan)
        pd_short['median_profit'] = pd_short['profit'].copy()

        pd_short['count_win'] = pd_short['p_win'].copy()
        pd_short['count_loss'] = pd_short['p_loss'].copy()
        pd_short['count_hold'] = pd_short['p_hold'].copy()
        pd_short['count_hold_win'] = pd_short['p_hold'].where(pd_short['p_hold'] > 0, np.nan)
        pd_short['count_hold_loss'] = pd_short['p_hold'].where(pd_short['p_hold'] <= 0, np.nan)
        pd_short['count_cutloss'] = pd_short['p_cutloss'].copy()

        pd_short['holding_period'] = (pd.to_datetime(pd_short['buy_time']) - pd.to_datetime(pd_short['time'])).dt.days
        pd_short["quarter"] = pd.to_datetime(pd_short['time']).dt.to_period('Q').astype(str)
        pd_short["quarter_fr"] = pd_short['time'].apply(lambda x: self.quarter_report(x))

        # pd_short['half_of_year'] = pd_short['time'].str[:4] + "H" + pd.to_datetime(pd_short['time']).dt.month.gt(6).add(
        #     1).astype(str)

        pd_short['deal'] = 1
        pd_short['trading_val'] = pd_short['Price'] * pd_short['Volume'] * 0.1
        pd_short['p_trading_val'] = (pd_short['profit'] / 100) * pd_short['trading_val']
        pd_short['trading_val_clip'] = (pd_short['Price'] * pd_short['Volume'] / pd_short['Trading_Session']).clip(
            upper=0.01)
        pd_short['p_trading_val_clip'] = pd_short['profit'] * pd_short['trading_val_clip']

        for ticker in pd_short['ticker'].unique():
            pd_short.loc[pd_short['ticker'] == ticker, 'corr'] = pd_short[pd_short['ticker'] == ticker]['profit'].corr(
                pd_short[pd_short['ticker'] == ticker]['profit_vni'])

        # for s_pattern in pd_short['filter'].unique():
        #     pd_short.loc[pd_short['filter'] == s_pattern, 'entropy'] = calculate_entropy(
        #         pd_short[pd_short['filter'] == s_pattern]['half_of_year'])
        #     pd_short.loc[pd_short['filter'] == s_pattern, 'n_half'] = \
        #         pd_short[pd_short['filter'] == s_pattern]['half_of_year'].unique().shape[0]
        return pd_short

    def weight_hits(self, df):
        df['p_win'] = df['p_sell'].where(df['p_sell'] > 0, np.nan)
        df['p_loss'] = df['p_sell'].where(df['p_sell'] <= 0, np.nan)
        df['holding_period'] = (pd.to_datetime(df['sell_time']) - pd.to_datetime(df['time'])).dt.days

        df["quarter"] = pd.to_datetime(df['time']).dt.to_period('Q').astype(str)
        df["quarter_fr"] = df['time'].apply(lambda x: self.quarter_report(x))

        for ticker in df['ticker'].unique():
            df.loc[df['ticker'] == ticker, 'corr_s2p_on_ticker'] = df[df['ticker'] == ticker]['score'].corr(
                df[df['ticker'] == ticker]['profit'])
        return df

    @staticmethod
    def calculate_entropy(df):
        # Count value
        counts = df.value_counts()
        # Count probabilities
        probabilities = counts / counts.sum()
        # entropy
        entropy = -np.sum(probabilities * np.log2(probabilities))

        return entropy

    @staticmethod
    def convert_to_yyyy_w(date_str):
        """
        Convert date to 'yyyy/w'.
        """

        def convert_date(date):
            year = date.year
            # month = date.month
            week = date.isocalendar()[1]
            return f"{year}-{week:02}"

        date_series = pd.to_datetime(date_str)
        return date_series.apply(convert_date)

    @staticmethod
    def quarter_report(str_time):
        year, month, day = str_time.split('-')
        time = int(month) * 100 + int(day)
        if time <= 131:  # Start year - Jan 30th -> move after 1 day
            key = f'{int(year) - 1}Q3'
        elif 131 < time <= 431:  # Feb 1st - April 31st -> move after 1 day
            key = f'{int(year) - 1}Q4'
        elif 431 < time <= 731:  # May 1st - July 31st -> move after 1 day
            key = f"{year}Q1"
        elif 731 < time <= 1031:  # Aug 1st -October 31st -> move after 1 day
            key = f"{year}Q2"
        else:  # Nov 1st - End year
            key = f"{year}Q3"
        return key


class Simulation:
    """
    Simulate trading strategy
    """

    def __init__(self, df_deals=None, start_date='2017-01-01', end_date='2026-01-01', initial_assets=1e8, max_deals=10,
                 num_proc=5, buffer_ratio=0.16, report=False, cache_service=None, combine=None, is_block_buy=False,
                 fpath='ticker_v1a'):
        self.df_deals = df_deals
        self.start_date = start_date
        self.end_date = self.preprocess_end_time(end_date)
        self.period, self.buffer = self.find_simulate_period(start_date, self.end_date, buffer_ratio)

        self.initial_assets = initial_assets
        self.max_deals = max_deals

        self.combine = self.preprocess_combine(combine)
        self.num_proc = num_proc
        self.report = report
        self.is_block_buy = is_block_buy
        self.fpath = fpath

        # init memory cache
        self.get_fist_data_each_years = cache_service.cache(self.get_fist_data_each_years)
        self.prepare_last_data_for_simulation = cache_service.cache(self.prepare_last_data_for_simulation)

        self.df_buffers = self.prepare_last_data_for_simulation(250 * 10, fpath=self.fpath)

        # self.buffer_data = self.prepare_last_data_for_simulation(buffer)

    @staticmethod
    def get_fist_data_each_years(ticker, fpath='ticker_v1a'):
        df_date = pd.read_csv(f'{fpath}/{ticker}.csv')
        df_date = df_date[['time', 'Open_1D']]
        df_date = df_date.copy()
        df_date['time'] = pd.to_datetime(df_date['time'])
        df_date['year'] = df_date['time'].dt.year
        df_date = df_date.groupby('year').first().reset_index()
        return df_date

    @staticmethod
    def prepare_last_data_for_simulation(buffer=250 * 10, fpath='ticker_v1a', max_threads=10, chunk_size=10):
        def read_ticker_data(ticker, fpath, buffer=250 * 10):
            ticker_data = pd.read_csv(f'{fpath}/{ticker}.csv', dtype={'time': str, 'Close': float},
                                      usecols=['time', 'Close'])
            ticker_data = ticker_data.tail(buffer + 20)
            ticker_data = ticker_data.rename(columns={'Close': ticker})
            return ticker_data

        list_tickers = [f.replace('.csv', '') for f in os.listdir(f'{fpath}/') if f.endswith('.csv')]

        result_df = None
        for i in range(0, len(list_tickers), chunk_size):
            chunk_tickers = list_tickers[i:i + chunk_size]

            with ThreadPoolExecutor(max_workers=max_threads) as executor:
                data_frames = list(executor.map(lambda ticker: read_ticker_data(ticker, fpath, buffer), chunk_tickers))

            chunk_df = pd.concat(data_frames, axis=0).groupby('time', as_index=False).max()

            if result_df is None:
                result_df = chunk_df
            else:
                result_df = result_df.merge(chunk_df, on=['time'], how='outer')

        # Sort the index
        result_df = result_df.sort_values('time', ascending=True).reset_index(drop=True)
        result_df = result_df.ffill()
        result_df['time_index'] = pd.to_datetime(result_df['time'])
        result_df = result_df.set_index('time_index')
        return result_df

    @staticmethod
    def preprocess_df_deals(df_deals, start_date, end_date):
        df = df_deals.reset_index(drop=True).copy()
        df = df.query("time >= @start_date and time <= @end_date")
        df = df[df['Volume_1M_P50'] > 0]
        df = df.sort_values('time', ascending=True).reset_index(drop=True)
        return df

    @staticmethod
    def preprocess_end_time(end_date):
        end_date = min(datetime.today(), datetime.strptime(end_date, "%Y-%m-%d")).strftime('%Y-%m-%d')
        return end_date

    @staticmethod
    def preprocess_combine(combines):
        res = []
        if isinstance(combines, str):
            if combines != "()":
                combines = combines.split("&")
                for c in combines:
                    c = c.replace("(", "").replace(")", "")
                    c = c.split(",")
                    res.append([s.strip() for s in c])
        return res

    @staticmethod
    def find_simulate_period(start_date, end_date, ratio):
        """
            Finds the simulation period based on the given start and end dates, and a buffer.

            The function takes the start and end dates, and a buffer value. It calculates the number of days between the end date and the start date, and subtracts the buffer value to get the simulation period.

            Parameters:
                start_date (str): The start date in the format "YYYY-MM-DD".
                end_date (str): The end date in the format "YYYY-MM-DD".
                buffer (int): The buffer value to subtract from the number of days.

            Returns:
                int: The simulation period in days.
            """
        delta_day = (datetime.strptime(end_date, "%Y-%m-%d") - datetime.strptime(start_date, "%Y-%m-%d")).days
        buffer = int(delta_day * ratio)
        simulate_period = int(delta_day - buffer)
        return simulate_period, buffer

    @staticmethod
    def convert_to_dict(serial_data):
        result = {}

        for item in serial_data.items():
            key, value = item
            main_key, sub_key = key.split('.', 1)

            if main_key not in result:
                result[main_key] = {}

            result[main_key][sub_key] = value

        return result

    @staticmethod
    def calculate_delta_price(V_sell, V_prev, k=0.1, P_latest=1):
        # Check for NaN inputs
        if np.isnan(V_sell) or np.isnan(V_prev):
            return 0.1

        if V_prev == 0 or V_sell == 0:
            return 0.1

        result = min(0.1, P_latest * (1 - np.exp(-k * (V_sell / V_prev))))

        # Check for NaN output
        if np.isnan(result):
            return 0.1

        return result

    @staticmethod
    def calculate_utilization(active_deals, state, df_data):
        """
        Calculate the utilization rate of funds in the market.

        Parameters:
        active_deals (pd.DataFrame): DataFrame containing active deals.
        state (dict): State dictionary containing deal information.
        df_data (pd.DataFrame): Market data DataFrame.

        Returns:
        tuple: (utilization_rate_list, updated_state)
        """
        invest_assets = 0
        delta_time = (datetime.strptime(state['deal_date'], "%Y-%m-%d") -
                      datetime.strptime(state['before_deal_date'], "%Y-%m-%d")).days
        utilization_rate = 0

        if delta_time < 0:
            delta_time = 0

        if delta_time != 0 and not active_deals.empty:
            tickers = active_deals['ticker'].unique().tolist()

            closest_date = df_data.index.asof(state['deal_date'])
            if pd.isna(closest_date):
                return [0] * delta_time, state

            try:
                df = df_data.loc[closest_date][tickers].to_frame().T
            except KeyError:
                # Filter ticker in df_data
                available_tickers = [t for t in tickers if t in df_data.columns]
                df_row = df_data.loc[closest_date]
                df = df_row[available_tickers].to_frame().T

                # Fill NaN which ticker wasn't existed
                for t in tickers:
                    if t not in df.columns:
                        df[t] = np.nan
            except (TypeError, IndexError):
                df = pd.DataFrame(columns=tickers)

            ticker_prices = active_deals['ticker'].map(lambda t: df[t].values[0] if t in df else np.nan)
            # price_filled = ticker_prices.fillna(active_deals['buy_price'])
            price_filled = np.where(pd.isna(ticker_prices), active_deals['buy_price'].values, ticker_prices.values)

            # Vectorized invest_assets
            invest_assets = 0.9975 * (
                    active_deals['investment_amount'] * (price_filled / active_deals['buy_price'])).sum()

            total_assets = invest_assets + state['available_assets']
            utilization_rate = invest_assets / total_assets if total_assets > 0 else 0

            # Update state
            state['before_deal_date'] = state['deal_date']
            state['total_assets'] = total_assets

        return [utilization_rate] * delta_time, state

    @staticmethod
    def calculate_purchase_value_in_block(deal, df_data, is_block):
        buy_ratio = 0.1
        upper = 1.03
        period = 10

        @lru_cache(maxsize=5)
        def cal_range_score(data):
            pass

        def buy_in_block(times, c_deal, df):
            """
            Buy through block, if times == 5. We will buy 5 times with price in 5 days. If price does not raise 3% compared with current price.
            The next day price will be in df
            """

            adjust_prices = []
            current_adjust_price = c_deal['Close']
            total_shares = 0

            buy_time = c_deal['time']
            sell_time = c_deal['sell_time']

            idx_start = df_data['time'].searchsorted(buy_time, side='right') - 1
            idx_sell = df_data['time'].searchsorted(sell_time, side='right') - 3
            idx_end = min(idx_start + period, idx_sell, len(df))

            buffer_data = df_data.iloc[idx_start:idx_end][c_deal['ticker']].to_frame()

            # _idx_start = df_data.index.asof(idx_time)
            # _idx_end = min(pd.to_datetime(c_deal['time']) + pd.Timedelta(days=period),
            #                pd.to_datetime(c_deal['sell_time']),
            #                df.tail(1).index)
            # _buffer_data = df_data.loc[_idx_start:_idx_end][c_deal['ticker']].to_frame()

            for i in range(buffer_data.shape[0]):
                if times == 0:
                    break
                next_day_adjust_price = buffer_data.iloc[i].values[0]
                if next_day_adjust_price <= current_adjust_price * upper:
                    total_shares += buy_ratio * c_deal['Volume_1M_P50']
                    adjust_prices.append(next_day_adjust_price)
                    times -= 1

            price_ratio = np.mean(adjust_prices) / current_adjust_price

            average_price = price_ratio * c_deal['Price']
            total_investment = total_shares * average_price

            return total_investment, average_price

        amount = buy_ratio * deal['Volume_1M_P50'] * deal['Price']
        price = deal['Price']
        if (RANKING in deal) and is_block:
            if 2 < deal[RANKING] <= 3:
                amount, price = buy_in_block(times=2, c_deal=deal, df=df_data)
            elif 3 < deal[RANKING] <= 4:
                amount, price = buy_in_block(times=3, c_deal=deal, df=df_data)
            elif 4 < deal[RANKING] <= 5:
                amount, price = buy_in_block(times=4, c_deal=deal, df=df_data)
            elif deal[RANKING] > 5:
                amount, price = buy_in_block(times=5, c_deal=deal, df=df_data)

        return amount, price

    def simulation(self, seed, si_type):
        """
        Simulate trading strategy for all filter string in df_deals.
        We will use full cash with 10 deals.
        :param seed: random seed
        :param si_type: type of simulation ["full_cash", "adjust_cash"]
        :return: dictionary of profit
        """

        def shuffle_by_date(pd_deals: pd.DataFrame, seed: int) -> pd.DataFrame:
            pd_deals = (pd_deals.groupby("time", group_keys=False)
                        .apply(lambda x: x.sample(frac=1, random_state=seed), include_groups=True)
                        .reset_index(drop=True))
            return pd_deals

        result = {}
        np.random.seed(seed)

        start_period = (pd.to_datetime(self.start_date) + pd.Timedelta(seed, "d")).strftime("%Y-%m-%d")
        end_period = (pd.to_datetime(start_period) + pd.Timedelta(self.period, "d")).strftime("%Y-%m-%d")

        df_deals = self.df_deals.query("@start_period <= time <= @end_period").copy()
        df_buffers = self.df_buffers.loc[
            (self.df_buffers["time"] >= start_period) & (self.df_buffers["time"] <= end_period)]

        df_deals = shuffle_by_date(df_deals, seed)
        # print(f"seed: {seed}, start_period: {start_period}, end_period: {end_period}, df_deals.shape: {df_deals.shape}")

        for b_pattern in df_deals['filter'].unique():
            # def simulate_trading(self, deals, df_data, start_date, end_date, initial_assets, initial_slot, si_type,
            #                      report=False, detail=False):
            result[b_pattern] = self.simulate_trading(deals=df_deals[df_deals['filter'] == b_pattern],
                                                      buffer_data_df=df_buffers, start_date=start_period,
                                                      end_date=end_period,
                                                      initial_assets=self.initial_assets, initial_slot=self.max_deals,
                                                      si_type=si_type, report=self.report)
        for combine in self.combine:
            result[str(combine)] = self.simulate_trading(deals=df_deals[df_deals['filter'].isin(combine)],
                                                         buffer_data_df=df_buffers, start_date=start_period,
                                                         end_date=end_period,
                                                         initial_assets=self.initial_assets,
                                                         initial_slot=self.max_deals,
                                                         si_type=si_type, report=self.report)

        return result

    def combine_simulation(self, seed, si_type):
        """
        Simulate trading strategy for all filter string in df_deals.
        We will use full cash with 10 deals.
        :param seed: random seed
        :param si_type: type of simulation ["full_cash", "adjust_cash"]
        :return: dictionary of profit
        """

        def shuffle_by_date(pd_deals: pd.DataFrame, seed: int) -> pd.DataFrame:
            pd_deals["_rand_order"] = np.random.RandomState(seed).rand(len(pd_deals))
            pd_deals = pd_deals.sort_values(["time", RANKING, "_rand_order"],
                                            ascending=[True, False, True]).drop(columns=["_rand_order"])
            return pd_deals

        result = {}
        np.random.seed(seed)

        start_period = (pd.to_datetime(self.start_date) + pd.Timedelta(seed, "d")).strftime("%Y-%m-%d")
        end_period = (pd.to_datetime(start_period) + pd.Timedelta(self.period, "d")).strftime("%Y-%m-%d")

        df_deals = self.df_deals.query("@start_period <= time <= @end_period").copy()
        df_deals = shuffle_by_date(df_deals, seed)

        df_buffers = self.df_buffers.loc[
            (self.df_buffers["time"] >= start_period) & (self.df_buffers["time"] <= end_period)]

        result['Combine'] = self.simulate_trading(deals=df_deals,
                                                  buffer_data_df=df_buffers, start_date=start_period,
                                                  end_date=end_period,
                                                  initial_assets=self.initial_assets, initial_slot=self.max_deals,
                                                  si_type=si_type, report=self.report)

        return result

    def simulate_trading(self, deals, buffer_data_df, start_date, end_date, initial_assets, initial_slot, si_type,
                         report=False, detail=False):
        log = []
        buffer_data = buffer_data_df.iloc[[buffer_data_df['time'].searchsorted(end_date, side='right') - 1]]

        available_assets = initial_assets
        active_deals = pd.DataFrame()  # Keep as DataFrame throughout
        completed_deals = []
        skip_deals = []
        utilization = [0]
        profit = 0
        peak_deals = 0

        # Report
        rf = 0.09
        profit_per_year = []
        previous_assets_report = initial_assets
        time_report = pd.to_datetime(start_date).replace(month=1, day=1) + pd.DateOffset(years=1)

        # time_in_market
        start_time = []
        end_time = []
        sell_time_remove = []

        state = {
            'available_assets': available_assets,
            'initial_slot': initial_slot,
            'initial_assets': initial_assets,
            'start_date': start_date,
            'end_date': end_date,
            'deal_date': start_date,
            'before_deal_date': start_date,
            'total_assets': initial_assets,
        }

        # Pre-process deals: calculate num_shares_to_sell for all deals at once
        deals_df = deals.copy()

        # Convert deals to list for processing
        deals_list = deals_df.to_dict('records') if not deals_df.empty else []

        for i, deal in enumerate(deals_list):
            # Ignore deal was blocked by force sell
            if deal['block']:
                continue

            # Report
            if report:
                while time_report <= pd.to_datetime(deal['time']):
                    current_assets_report = available_assets
                    for _, d in active_deals.iterrows():
                        df = self.get_fist_data_each_years(ticker=d['ticker'])
                        df = df.query(f"time >= @time_report").head(1)
                        current_assets_report += (d['investment_amount'] * (df['Open_1D'] / d['Open_1D'])).values[0]

                    profit_per_year.append((current_assets_report - previous_assets_report) / previous_assets_report)
                    previous_assets_report = current_assets_report
                    time_report = time_report + pd.DateOffset(years=1)

            # process complete deal

            # Utilization
            state['deal_date'] = deal['time']
            utilize, state = self.calculate_utilization(active_deals, state, buffer_data_df)
            utilization.extend(utilize)

            # Vectorized processing of active deals
            if not active_deals.empty:
                # Create boolean mask for deals to be sold
                sell_mask = active_deals['sell_time'] <= deal['time']

                # Process deals to be sold (vectorized)
                if sell_mask.any():
                    deals_to_sell = active_deals[sell_mask].copy()
                    deals_to_sell['num_shares_to_sell'] = deals_to_sell.groupby(['ticker', 'sell_time'])[
                        'num_shares_purchased'].transform('sum')

                    # Vectorized profit calculation
                    deals_to_sell['delta'] = deals_to_sell.apply(self.check_to_take_profit_deal, axis=1)
                    deals_to_sell['adj_sell_price'] = deals_to_sell['sell_price'] * (1 - deals_to_sell['delta'])
                    deals_to_sell['profit'] = (deals_to_sell['adj_sell_price'] - deals_to_sell['buy_price']) / \
                                              deals_to_sell['buy_price'] * 100

                    # Update profit and available assets (vectorized)
                    total_profit = deals_to_sell['profit'].sum()
                    profit += total_profit

                    # Vectorized assets update
                    profit_ratios = deals_to_sell['profit'] / 100
                    assets_to_add = (deals_to_sell['investment_amount'] * (1 + profit_ratios) * 0.9975).sum()
                    available_assets += assets_to_add

                    # Add to completed deals
                    completed_deals.extend(deals_to_sell.to_dict('records'))

                    # Add sell times for time_in_market calculation
                    sell_time_remove.extend(deals_to_sell['sell_time'].tolist())

                    if detail:
                        log_active_deals = [
                            {
                                "ticker": d['ticker'],
                                "filter": d['filter'],
                                "investment": d['investment_amount'],
                                "investment_ratio": (d['investment_amount'] / state['total_assets']) * 100,
                                "num_of_shares_held": d['num_shares_purchased'],
                                "buy_date": d['time'],
                            }
                            for _, d in active_deals[~sell_mask].iterrows()
                        ]

                        # Vectorized log creation
                        for _, sold_deal in deals_to_sell.iterrows():
                            log_entry = {
                                "action": "Sell",
                                "date": sold_deal['sell_time'],
                                "ticker": sold_deal['ticker'],
                                "filter": sold_deal['sell_filter'],
                                "profit_percentage": round(sold_deal['profit'], 2),
                                "num_of_shares_held": sold_deal['num_shares_purchased'],
                                "daily_trading_volume": sold_deal['Volume_sell'],
                                "buy_date": sold_deal['time'],
                                "holding_days": sold_deal['holding_period'],
                                "remaining_cash": round(available_assets, 2),
                                "utilization": utilization[-1],
                                "total_assets": state['total_assets'],
                                "active_deals": log_active_deals
                            }
                            log.append(json.dumps(log_entry))

                # Keep deals that are not sold yet
                active_deals = active_deals[~sell_mask].copy()

            # end - time_in_market
            if active_deals.empty and sell_time_remove:
                end_time.append(max(sell_time_remove))

            # Check the budget for the new deal
            state['available_assets'] = available_assets
            investment_amount, investment_price, state = self.check_to_buy_new_deal(deal, active_deals, buffer_data_df,
                                                                                    state,
                                                                                    si_type=si_type)

            if investment_amount:
                deal['investment_amount'] = investment_amount * 0.9985
                deal['num_shares_purchased'] = deal['investment_amount'] / investment_price
                # for buy tax
                available_assets -= investment_amount

                # start - time_in_market
                if active_deals.empty:
                    start_time.append(deal['time'])

                # Add new deal to active_deals DataFrame
                new_deal_df = pd.DataFrame([deal])
                active_deals = pd.concat([active_deals, new_deal_df], ignore_index=True)
                peak_deals = max(peak_deals, len(active_deals))

                if detail:
                    #  Add logs
                    log_entry = {
                        "action": "Buy",
                        "date": deal['time'],
                        "ticker": deal['ticker'],
                        "filter": deal['filter'],
                        "investment": round(investment_amount, 2),
                        "price": investment_price,
                        "num_of_shares_held": deal['num_shares_purchased'],
                        "daily_trading_volume": deal['Volume'],
                        "remaining_cash": round(available_assets, 2),
                        "utilization": utilization[-1],
                        "total_assets": state['total_assets'],
                        "active_deals": [
                            {
                                "ticker": d['ticker'],
                                "filter": d['filter'],
                                "investment": d['investment_amount'],
                                "investment_ratio": (d['investment_amount'] / state['total_assets']) * 100,
                                "num_of_shares_held": d['num_shares_purchased'],
                                "buy_date": d['time'],
                            }
                            for _, d in active_deals.iterrows()
                        ]
                    }
                    log.append(json.dumps(log_entry))

            else:
                skip_deals.append(deal)

        # Final processing of remaining active deals (vectorized)
        if not active_deals.empty:
            # Update sell_time and sell_price for deals extending beyond end_date
            end_date_mask = pd.to_datetime(active_deals['sell_time']) > pd.to_datetime(end_date)
            if end_date_mask.any():
                active_deals.loc[end_date_mask, 'sell_time'] = end_date
                # active_deals.loc[end_date_mask, 'sell_price'] = buffer_data.at[buffer_data.index[0], active_deals.loc[end_date_mask, 'ticker']]
                active_deals.loc[end_date_mask, 'sell_price'] = (
                    active_deals.loc[end_date_mask, 'ticker']
                    .map(lambda t: buffer_data.at[buffer_data.index[0], t])
                )

            # Calculate num_shares_to_sell for remaining deals
            active_deals['num_shares_to_sell'] = active_deals.groupby(['ticker', 'sell_time'])[
                'num_shares_purchased'].transform('sum')
            # Vectorized profit calculation for all remaining deals
            active_deals['delta'] = active_deals.apply(self.check_to_take_profit_deal, axis=1)
            active_deals['adj_sell_price'] = active_deals['sell_price'] * (1 - active_deals['delta'])
            active_deals['profit'] = (active_deals['adj_sell_price'] - active_deals['buy_price']) / active_deals[
                'buy_price'] * 100

            # Update profit and assets (vectorized)
            total_profit = active_deals['profit'].sum()
            profit += total_profit

            profit_ratios = active_deals['profit'] / 100
            assets_to_add = (active_deals['investment_amount'] * (1 + profit_ratios) * 0.9975).sum()
            available_assets += assets_to_add

            # Add to completed deals
            completed_deals.extend(active_deals.to_dict('records'))

            # Add sell times
            sell_time_remove.extend(active_deals['sell_time'].tolist())

            if detail:
                # Vectorized log creation for final deals
                for _, final_deal in active_deals.iterrows():
                    log_entry = {
                        "action": "Sell",
                        "date": final_deal['sell_time'],
                        "ticker": final_deal['ticker'],
                        "filter": final_deal['sell_filter'],
                        "profit_percentage": round(final_deal['profit'], 2),
                        "num_of_shares_held": final_deal['num_shares_purchased'],
                        "daily_trading_volume": final_deal['Volume_sell'],
                        "buy_date": final_deal['time'],
                        "holding_days": final_deal['holding_period'],
                        "remaining_cash": round(available_assets, 2),
                        "utilization": utilization[-1],
                        "total_assets": state['total_assets'],
                        "active_deals": []
                    }
                    log.append(json.dumps(log_entry))

        if sell_time_remove:
            end_time.append(max(sell_time_remove))
        else:
            end_time.append(end_date)
        time_in_market = sum(
            [(pd.to_datetime(end_time[i]) - pd.to_datetime(start_time[i])).days for i in range(len(start_time))])
        total_time = (pd.to_datetime(end_date) - pd.to_datetime(start_date)).days - 1

        utilization = np.nanmean(utilization)
        df_comp = pd.DataFrame(completed_deals)
        if df_comp.empty:
            result = {
                'match_deals': 0,
                'total_time': total_time,
                'time_in_market': time_in_market,
                'profit': 0,
                'cash_profit': ((available_assets / initial_assets) - 1) * 100,
                'return': ((available_assets / initial_assets) ** (365 / total_time) - 1) * 100,
                'available_assets': available_assets,
                'utilization': utilization,
                'sharpe_ratio': 0,
                'win_deal': 0,
                'win_quarter': 0,
                'set_ticker': [],
                'set_quarter_ticker': [],
                'peak_number_deals': peak_deals,
            }
        else:
            df_comp['count'] = 1
            df_comp['win_count'] = df_comp['profit'].apply(lambda x: 1 if x > 0 else np.nan)
            df_comp_q = df_comp.groupby('quarter', as_index=False).agg(
                {'profit': 'sum', 'win_count': 'count', 'count': 'count'})

            win_deal = df_comp[df_comp['profit'] > 0].shape[0] / df_comp.shape[0]
            win_quarter = df_comp_q[df_comp_q['win_count'] / df_comp_q['count'] > 0.5].shape[0] / df_comp_q.shape[0]

            set_ticker = list(set([d['ticker'] for d in completed_deals]))
            set_quarter_ticker = list(set([(d['ticker'], d['quarter_fr']) for d in completed_deals]))

            result = {
                'match_deals': len(completed_deals),
                'total_time': total_time,
                'time_in_market': time_in_market,
                'profit': profit / len(completed_deals),
                'cash_profit': ((available_assets / initial_assets) - 1) * 100,
                'return': ((available_assets / initial_assets) ** (365 / total_time) - 1) * 100,
                'available_assets': available_assets,
                'utilization': utilization,
                'sharpe_ratio': ((np.mean(profit_per_year) - rf) / np.std(profit_per_year)) if report else 0,
                'win_deal': win_deal * 100,
                'win_quarter': win_quarter * 100,
                'set_ticker': set_ticker,
                'set_quarter_ticker': set_quarter_ticker,
                'peak_number_deals': peak_deals,
            }
        if detail:
            result['completed_deals'] = pd.DataFrame(completed_deals) if completed_deals else pd.DataFrame()
            result['skip_deals'] = pd.DataFrame(skip_deals) if skip_deals else pd.DataFrame()
            result['log'] = '\n'.join(log)

        return result

    def check_to_take_profit_deal(self, active_deal):
        delta = self.calculate_delta_price(active_deal['num_shares_to_sell'], active_deal['Volume_sell'])

        if active_deal['Low_sell'] == active_deal['High_sell'] == active_deal['Open_sell'] == active_deal[
            'Close_sell'] == active_deal['Close_T1_sell']:
            delta = max(delta, MAX_DECREASE)

        return delta

    def check_to_buy_new_deal(self, current_deal, active_deals, df_data, state: dict, si_type):
        available_assets = state.get('available_assets', 0)
        initial_slot = state.get('initial_slot', 10)
        initial_assets = state.get('initial_assets', 10e8)
        start_date = state.get('start_date', '2014-01-01')
        end_size = state.get('end_size',
                             pd.to_datetime(start_date).replace(month=1, day=1) + pd.DateOffset(years=1))

        investment, investment_price = self.calculate_purchase_value_in_block(current_deal, df_data, self.is_block_buy)
        investment_amount = 0

        # Count active deals as DataFrame or list
        if hasattr(active_deals, 'empty'):
            active_deals_count = 0 if active_deals.empty else len(active_deals)
        else:
            active_deals_count = len(active_deals)

        if si_type == SIMULATE_FULL_CASH:
            if active_deals_count < initial_slot:
                investment_amount = min(investment, (available_assets / (initial_slot - active_deals_count)))

        if si_type == SIMULATE_FULL_CASH_NOT_FIX:
            min_amount = 0.5 * (initial_assets / initial_slot)
            if available_assets > min_amount:
                if active_deals_count < initial_slot:
                    investment_amount = min(investment, (available_assets / (initial_slot - active_deals_count)))
                else:
                    investment_amount = min(investment, available_assets)

        if si_type == SIMULATE_ALLOCATE:
            # Update size
            total_assets = state['total_assets']
            cash_size = total_assets / initial_slot

            # Check the budget for the new deal
            amount = min(investment, cash_size)
            if available_assets >= amount:
                investment_amount = amount

        state['available_assets'] = available_assets
        state['initial_slot'] = initial_slot
        state['initial_assets'] = initial_assets
        state['end_size'] = end_size

        return investment_amount, investment_price, state

    def run_fast(self, df_deals, iterate=10, s_type=SIMULATE_FULL_CASH, handle=None, r_col=None):
        """
            Run fast simulation on deals data.

            Args:
                df_deals (pd.DataFrame): Dataframe containing deals data.
                iterate (int): Number of iterations for simulation.
                s_type (str): Simulation type, default is 'full_cash'.
                handle (str): Handling method, if 'combine' uses combine_simulation.
                r_col (str): Column name for ranking point.

            Returns:
                Dict[str, Any]: Dictionary containing simulation results.
            """
        from pathos.multiprocessing import ProcessingPool as Pool

        self.df_deals = self.preprocess_df_deals(df_deals, start_date=self.start_date, end_date=self.end_date, )

        np.random.seed(iterate)
        iterate_range = np.linspace(0, self.buffer, iterate + 1, dtype=int)
        random_list = [np.random.randint(r[0], r[1]) for r in zip(iterate_range[:-1], iterate_range[1:])]
        #
        try:
            with Pool(processes=self.num_proc) as pool:
                # all_results = pool.map(self.simulation, [None] * iterate)
                if handle == 'combine':
                    self.df_deals[RANKING] = self.df_deals[r_col]
                    # self.df_deals.rename(columns={r_col: RANKING}, inplace=True)
                    self.df_deals = self.df_deals[self.df_deals[RANKING] > 0]
                    # Deduplicate rows with the same ticker in the same time
                    self.df_deals = self.df_deals.drop_duplicates(subset=['ticker', 'time'], keep='first').reset_index(
                        drop=True)

                    all_results = pool.map(self.combine_simulation, random_list, [s_type] * iterate)
                else:
                    all_results = pool.map(self.simulation, random_list, [s_type] * iterate)

            df = pd.json_normalize(all_results)
            df_result = pd.Series(index=df.columns)

            for col in df.columns:
                if 'return' in col:
                    df_result[col] = df[col].mean(skipna=True)
                    df_result[col.replace('return', 'return_std')] = df[col].std(skipna=True)
                    df_result[col.replace('return', 'return_max')] = df[col].max(skipna=True)
                    df_result[col.replace('return', 'return_min')] = df[col].min(skipna=True)
                elif ('set_ticker' in col) or ('set_quarter_ticker' in col):
                    lengths = [len(v) for v in df[col].values]
                    tickers = [ticker for v in df[col].values for ticker in v]
                    df_result[col] = len(set(tickers)) / np.mean(lengths)
                    df_result[col.replace('set', 'unique')] = len(set(tickers))

                else:
                    df_result[col] = df[col].mean(skipna=True)

            return self.convert_to_dict(df_result)
        except Exception as e:
            print(f"An error occurred during simulation: {str(e)}")
            return {}

    def get_detail(self, s_type):
        def shuffle_by_date(pd_deals: pd.DataFrame, seed: int) -> pd.DataFrame:
            pd_deals["_rand_order"] = np.random.RandomState(seed).rand(len(pd_deals))
            pd_deals = pd_deals.sort_values(["time", RANKING, "_rand_order"],
                                            ascending=[True, False, True]).drop(columns=["_rand_order"])
            return pd_deals

        np.random.seed(1)

        df_deals = shuffle_by_date(self.df_deals, 1)

        result = self.simulate_trading(deals=df_deals, buffer_data_df=self.df_buffers, start_date=self.start_date,
                                       end_date=self.end_date, initial_assets=self.initial_assets,
                                       initial_slot=self.max_deals, si_type=s_type, report=self.report, detail=True)
        return result


@memory.cache
def prepare_last_data_for_simulation(buffer=250 * 10, fpath='ticker_v1a', max_threads=10, chunk_size=10):
    def read_ticker_data(ticker, fpath, buffer=250 * 10):
        ticker_data = pd.read_csv(f'{fpath}/{ticker}.csv', dtype={'time': str, 'Close': float},
                                  usecols=['time', 'Close'])
        ticker_data = ticker_data.tail(buffer + 20)
        ticker_data = ticker_data.rename(columns={'Close': ticker})
        return ticker_data

    list_tickers = [f.replace('.csv', '') for f in os.listdir(f'{fpath}/') if f.endswith('.csv')]

    result_df = None
    for i in range(0, len(list_tickers), chunk_size):
        chunk_tickers = list_tickers[i:i + chunk_size]

        with ThreadPoolExecutor(max_workers=max_threads) as executor:
            data_frames = list(executor.map(lambda ticker: read_ticker_data(ticker, fpath, buffer), chunk_tickers))

        chunk_df = pd.concat(data_frames, axis=0).groupby('time', as_index=False).max()

        if result_df is None:
            result_df = chunk_df
        else:
            result_df = result_df.merge(chunk_df, on=['time'], how='outer')

    result_df = result_df.sort_values('time', ascending=True).reset_index(drop=True)
    result_df['time_index'] = pd.to_datetime(result_df['time'])
    result_df = result_df.set_index('time_index')
    return result_df
